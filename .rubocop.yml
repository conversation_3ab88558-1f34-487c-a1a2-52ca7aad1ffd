# Omakase Ruby styling for Rails
inherit_gem: { rubocop-rails-omakase: rubocop.yml }

# Overwrite or add rules to create your own house style
#
# # Use `[a, [b, c]]` not `[ a, [ b, c ] ]`
# Layout/SpaceInsideArrayLiteralBrackets:
#   Enabled: false

AllCops:
  SuggestExtensions: true
  Exclude:
    - "db/seeds.rb"
    - "db/seeds/**/*"
    - "db/schema.rb"
    - "vendor/**/*"
  DisplayStyleGuide: true
  ExtraDetails: true
  TargetRubyVersion: 3.4
  NewCops: enable

Rails:
  Enabled: true

Layout/LineLength:
  Max: 120
