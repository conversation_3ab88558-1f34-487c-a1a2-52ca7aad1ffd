class Api::V1::CustomersController < Api::V1::BaseController
  def index
    @pagy, @customers = pagy(
      dealership.customers.ordered_by_name,
      items: pagination_validated_per_page,
      page: params[:page] || 1
    )

    set_pagination_headers(@pagy)

    render_success_response("Customers retrieved successfully", {
      customers: Api::V1::CustomerSerializer.render_as_json(@customers)
    })
  end

  def create
    customer = dealership.customers.build(customer_params)
    customer.save!

    render_success_response("Customer created successfully", {
      customer: Api::V1::CustomerSerializer.render_as_json(customer)
    })
  end

  def show
    render_success_response("Customer retrieved successfully", {
      customer: Api::V1::CustomerSerializer.render_as_json(customer)
    })
  end

  def update
    customer.update!(customer_params)
    render_success_response("Customer updated successfully", {
      customer: Api::V1::CustomerSerializer.render_as_json(customer.reload)
    })
  end

  def search
    query = search_params[:query]&.strip

    if query.blank? || query.length < 3 || query.length > 20
      return render_success_response("Customer search completed successfully", {
        customers: []
      })
    end
    # Limit results to prevent performance issues
    customers = dealership.customers.search_by_term(query)

    render_success_response("Customer search completed successfully", {
        customers: serialize_search_results(customers)
      })
  end

  private

  def serialize_search_results(customers)
    return [] if customers.empty?

    customers.map do |customer|
      {
        uuid: customer.uuid,
        full_name: customer.full_name,
        email: customer.email,
        phone_number: customer.phone_number,
        created_at: customer.created_at
      }
    end
  end

  def customer_params
    params.permit(
      :first_name, :last_name, :email, :phone_number, :age, :gender,
      :company_name, :external_id, :postcode, :suburb, :address_line1,
      :address_line2, :city, :state, :country
    )
  end

  def search_params
    params.permit(:dealership_uuid, :query)
  end
end
