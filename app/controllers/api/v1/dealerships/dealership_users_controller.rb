class Api::V1::Dealerships::DealershipUsersController < Api::V1::BaseController
  before_action :validate_role_filter, only: [ :index ]

  def index
    @pagy, @users = pagy(
      filtered_users,
      items: pagination_validated_per_page,
      page: permitted_params[:page] || 1
    )

    set_pagination_headers(@pagy)

    render_success_response("Dealership users retrieved successfully", {
      users: Api::V1::UserSerializer.render_as_json(@users, dealership: dealership)
    })
  end

  private

  def filtered_users
    dealership_users = dealership.users.active.includes(:user_dealerships).ordered_by_name

    return dealership_users unless @role.present?

    dealership_users.joins(:user_dealerships)
              .where(user_dealerships: { dealership_id: dealership.id, role: @role })
              .distinct
  end

  def validate_role_filter
    @role = permitted_params[:role_type]
    return unless @role.present?

    unless UserDealership.roles.key?(@role)
      raise Errors::InvalidInput, "Invalid role filter. Valid roles: #{UserDealership.roles.keys.join(', ')}"
    end
  end

  def permitted_params
    params.permit(:dealership_uuid, :page, :per_page, :role_type)
  end
end
