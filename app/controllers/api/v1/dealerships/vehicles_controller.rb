class Api::V1::Dealerships::VehiclesController < Api::V1::BaseController
  before_action :validate_type_filter, only: [ :index ]
  def index
    @pagy, @vehicles = pagy(
      filtered_vehicles,
      items: pagination_validated_per_page,
      page: permitted_params[:page] || 1
    )

    set_pagination_headers(@pagy)

    render_success_response("Dealership vehicles retrieved successfully", {
      vehicles: Api::V1::VehicleSerializer.render_as_json(@vehicles)
    })
  end
  def create
    # Create vehicle with processed params
    vehicle = dealership.vehicles.build(vehicle_params.except(:brand_uuid))
    vehicle.brand = find_brand(vehicle_params[:brand_uuid]) if vehicle_params[:brand_uuid].present?
    vehicle.save!
    render_creation_response("Vehicle created successfully", {
      vehicle: Api::V1::VehicleSerializer.render_as_json(vehicle)
    })
  end


  private

  def vehicle_params
    params.expect(vehicle: [ :brand_uuid, :vin, :stock_number, :rego, :make, :model, :build_year, :color, :rego_expiry, :status,
                  :vehicle_type, :is_trade_plate_used, :available_for_drive, :last_known_odometer_km,
                  :last_known_fuel_gauge_level, photos: [] ])
  end

  def filtered_vehicles
    dealership_vehicles = dealership.vehicles.not_deleted.ordered_by_name

    if @vehicle_type.present?
      dealership_vehicles.where(vehicle_type: @vehicle_type)
    else
      dealership_vehicles
    end
  end

  def validate_type_filter
    @vehicle_type = permitted_params[:vehicle_type]
    return if @vehicle_type.blank?

    unless DealershipVehicle.vehicle_types.key?(@vehicle_type)
      raise Errors::InvalidInput, "Invalid vehicle type filter. Valid filters: #{DealershipVehicle.vehicle_types.keys.join(', ')}"
    end
  end

  def permitted_params
    params.permit(:dealership_uuid, :page, :per_page, :vehicle_type)
  end
end
