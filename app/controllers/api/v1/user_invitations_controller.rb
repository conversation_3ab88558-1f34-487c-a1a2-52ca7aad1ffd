class Api::V1::UserInvitationsController < Api::V1::BaseController
  def create
    service = UserInvitationService.new(current_user)
    user = service.invite_user(
      email: permitted_params[:email],
      first_name: permitted_params[:first_name],
      last_name: permitted_params[:last_name],
      phone: permitted_params[:phone],
      user_type: permitted_params[:user_type],
      dealership_uuid: permitted_params[:dealership_uuid],
      role: permitted_params[:role_type]
    )

    render_success_response(
      "User invitation sent successfully",
      user: Api::V1::UserSerializer.render_as_json(user, dealership: user.dealerships.first)
    )
  end

  private

  def permitted_params
    params.require(:user).permit(:email, :first_name, :last_name, :phone, :user_type, :dealership_uuid, :role_type)
  end
end
