module ResponseHandler
  extend ActiveSupport::Concern

  included do
    before_action :skip_session
    respond_to :json
    skip_before_action :verify_authenticity_token

    # The order matters! The priority is from bottom to top
    rescue_from StandardError, with: :render_internal_server_error
    rescue_from ActiveRecord::RecordInvalid, with: :render_unprocessable_with_errors
    rescue_from ActiveRecord::RecordNotFound, Errors::UserNotFound, Errors::RecordNotFound,
                with: :render_not_found
    rescue_from ActionController::ParameterMissing, ArgumentError, with: :render_bad_request
    rescue_from Errors::AuthenticationError, Errors::MissingDeviceId, Errors::InvalidToken,
                Errors::TokenExpired, Errors::InvalidDevice, Errors::InvalidSession,
                Errors::InvalidRefreshToken, Errors::RefreshTokenExpired, with: :render_unauthorized
    rescue_from Errors::InvalidInput, with: :render_unprocessable
    rescue_from Errors::TooManyRequestError, with: :render_rate_limited
    rescue_from Errors::InternalError, with: :render_internal_server_error
    rescue_from Errors::ForbiddenError, with: :render_forbidden_error
  end

  private

  def skip_session
    request.session_options[:skip] = true
  end

  # Helper method for rendering success responses with consistent structure
  def render_success_response(message, data = {})
    response = {
      status: {
        code: 200,
        message: message
      }
    }
    response[:data] = data unless data.empty?
    render json: response, status: :ok
  end

  # Helper method for rendering success creation with consistent structure
  def render_creation_response(message, data = {})
    response = {
      status: {
        code: 201,
        message: message
      }
    }
    response[:data] = data unless data.empty?
    render json: response, status: :created
  end

  # log the error for observability
  def log_error(message, level: :warn, exception: nil, print_backtrace: false)
    # Add any more observability here
    Rails.logger.public_send(level, message)
    Rails.logger.public_send(level, exception.backtrace.join("\n")) if exception&.backtrace.present? && print_backtrace
  end

  # log and render the response
  def render_error(message, code, status, level: :warn, exception: nil, print_backtrace: false)
    log_error(message, level: level, exception: exception, print_backtrace: print_backtrace)
    render json: {
      status: {
        code: code,
        message: message
      }
    }, status: status
  end

  # renders 404 error code
  def render_not_found(exception)
    render_error(exception.message, 404, :not_found)
  end

  # renders 422 error code
  def render_unprocessable(exception)
    render_error(exception.message, 422, :unprocessable_entity)
  end

  def render_unprocessable_with_errors(exception)
    log_error(exception.message, level: :warn, exception: exception)
    render "api/v1/shared/errors", locals: { object: exception.record, code: 422, message: exception.message }, status: :unprocessable_entity
  end

  # renders 400 error code
  def render_bad_request(exception)
    render_error(exception.message, 400, :bad_request)
  end

  def render_internal_server_error(exception)
    render_error(exception.message, 500, :internal_server_error, level: :error, exception: exception, print_backtrace: true)
  end

  def render_unauthorized(exception)
    render_error(exception.message, 401, :unauthorized, level: :warn, exception: exception)
  end

  def render_rate_limited(exception)
    render_error(exception.message, 429, :too_many_requests, level: :warn, exception: exception)
  end

  def render_forbidden_error(exception)
    render_error(exception.message, 403, :forbidden, level: :warn, exception: exception)
  end
end
