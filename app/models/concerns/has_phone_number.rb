module HasPhoneNumber
  extend ActiveSupport::Concern

  class_methods do
    def has_phone_number(field_name: :phone, required: false)
      validates field_name, phone: {
        possible: false
      }, presence: required, allow_blank: !required

      define_method "#{field_name}=" do |value|
        return super(nil) if value.blank?

        parsed = Phonelib.parse(value)
        if parsed.valid?
          super(parsed.e164)
        else
          super(value)
        end
      end

      define_method "formatted_#{field_name}" do
        return nil unless send(field_name).present?
        parsed = Phonelib.parse(send(field_name))
        parsed.national
      end
    end
  end
end
