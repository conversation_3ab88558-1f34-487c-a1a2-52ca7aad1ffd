class Customer < ApplicationRecord
  include HasUuid

  belongs_to :dealership
  has_many :drives, dependent: :destroy
  has_one :driver_license, as: :holder, dependent: :destroy
  accepts_nested_attributes_for :driver_license

  # Validation
  validates :first_name, :last_name, :email, presence: true
  validates :company_name, length: { maximum: 255, minimum: 3 }, allow_blank: true
  validates :email, format: { with: URI::MailTo::EMAIL_REGEXP }
  validates :email, uniqueness: { scope: :dealership_id, message: "Customer with this email already exists in this dealership" }
  validates :phone_number, length: { maximum: 15, minimum: 6 }, presence: true
  validates :external_id, length: { maximum: 50, minimum: 1 }, allow_blank: true

  # Enum
  enum :gender, {
    unspecified: 0,
    male: 1,
    female: 2,
    other: 3
  }, default: :unspecified

  # Scopes
  scope :search_by_term, ->(term, limit: 20) {
    return none if term.blank?

    # Reject if less than 3 characters or more than 20 characters
    cleaned_term = term.strip
    return none if cleaned_term.length < 3 || cleaned_term.length > 20

    search_term = "%#{cleaned_term.downcase}%"
    where(
      "LOWER(first_name) LIKE :term OR
       LOWER(last_name) LIKE :term OR
       LOWER(email) LIKE :term OR
       LOWER(phone_number) LIKE :term",
      term: search_term
    ).order(:first_name, :last_name).limit(limit)
  }

  scope :ordered_by_name, -> { order(:first_name, :last_name) }


  def full_name
    "#{first_name} #{last_name}"
  end

  def to_param
    uuid
  end
end
