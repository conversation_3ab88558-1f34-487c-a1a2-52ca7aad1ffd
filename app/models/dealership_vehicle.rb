class DealershipVehicle < ApplicationRecord
  include HasUuid
  include VehicleBase

  has_many :drives, dependent: :destroy, foreign_key: "vehicle_id"
  has_many :damage_reports, foreign_key: "vehicle_id"
  has_one :last_damage_report, -> { where(report_type: DamageReport::VEHICLE) }, class_name: "DamageReport", dependent: :destroy, foreign_key: "vehicle_id"
  has_one :last_known_location, as: :trackable, class_name: "GpsLocation", dependent: :destroy
  belongs_to :brand, optional: true

  enum :status, {
    available: 0,
    in_use: 1,
    out_of_service: 2,
    deleted: 3,
    sold: 4,
    enquiry: 5
  }, default: :available

  enum :vehicle_type, {
    new_vehicle: 0,
    demo: 1,
    old: 2
  }

  scope :available_for_test_drive, -> { where(status: :available) }
  scope :not_deleted, -> { where.not(status: :deleted) }

  def currently_on_test_drive?
    drives.active.exists?
  end
end
