class Drive < ApplicationRecord
  include HasUuid

  # Drive types where vehicle is not taken out of dealership, its just an advance booking
  BOOKING_TYPES = %w[test_drive_booking loan_booking].freeze

  # Drive types where vehicle is taken out of dealership
  VEHICLE_OUT_DRIVE_TYPES = %w[test_drive loan self_loan].freeze

  # Associations
  belongs_to :dealership
  belongs_to :vehicle, class_name: "DealershipVehicle"
  belongs_to :customer, optional: true
  belongs_to :driver_license, optional: true
  belongs_to :sales_person, class_name: "User"
  has_one :initial_damage_report, -> { where(report_type: DamageReport::INITIAL) }, class_name: "DamageReport", dependent: :destroy, inverse_of: :drive
  has_one :final_damage_report, -> { where(report_type: DamageReport::FINAL) }, class_name: "DamageReport", dependent: :destroy, inverse_of: :drive
  belongs_to :sales_person_accompanying, class_name: "User", optional: true
  belongs_to :trade_plate, optional: true
  has_many :waypoints, -> { order(created_at: :asc) }, as: :trackable, class_name: "GpsLocation", dependent: :destroy, inverse_of: :trackable

  accepts_nested_attributes_for :customer

  validates :end_datetime, comparison: { greater_than: :start_datetime, message: "must be after start time" }, # rubocop:disable Rails/I18nLocaleTexts
            if: -> { start_datetime.present? && end_datetime.present? }
  validates :expected_return_datetime, comparison: { greater_than: :expected_pickup_datetime, message: "must be after pickup time" }, # rubocop:disable Rails/I18nLocaleTexts
            if: -> { expected_pickup_datetime.present? && expected_return_datetime.present? }
  validate :sales_person_belongs_to_dealership

  enum :status, {
    scheduled: 0,
    in_progress: 1,
    completed: 2,
    cancelled: 3,
    draft: 4,
    deleted: 5
  }, default: :scheduled

  default_scope { where.not(status: :deleted) }

  enum :drive_type, {
    test_drive: 0,
    enquiry: 1,
    loan: 2,
    appraisal: 3,
    loan_booking: 4,
    test_drive_booking: 5,
    self_loan: 6
  }, default: :test_drive

  enum :sold_status, {
    unsold: 0,
    sold: 1
  }, default: :unsold

  scope :active, -> { where(status: :in_progress) }
  scope :bookings, -> { where(drive_type: BOOKING_TYPES) }
  scope :vehicle_out_type_drives, -> { where(drive_type: VEHICLE_OUT_DRIVE_TYPES) }

  scope :pickup_between_dates, ->(start_date = nil, end_date = nil) {
    query = all
    begin
      query = query.where(expected_pickup_datetime: Date.parse(start_date).beginning_of_day..) if start_date.present?
      query = query.where(expected_pickup_datetime: ..Date.parse(end_date).end_of_day) if end_date.present?
    rescue Date::Error, ArgumentError
      raise Errors::InvalidInput, "Invalid date format. Use YYYY-MM-DD format"
    end
    query
  }

  scope :updated_between_dates, ->(start_date = nil, end_date = nil) {
    query = all
    begin
      query = query.where(drives: { updated_at: Date.parse(start_date).beginning_of_day.. }) if start_date.present?
      query = query.where(drives: { updated_at: ..Date.parse(end_date).end_of_day }) if end_date.present?
    rescue Date::Error, ArgumentError
      raise Errors::InvalidInput, "Invalid date format. Use YYYY-MM-DD format"
    end
    query
  }

  scope :filter_by_status, ->(status) { where(status: status) }
  scope :filter_by_drive_type, ->(drive_type) { where(drive_type: drive_type) }
  scope :filter_by_sold_status, ->(sold_status) { where(sold_status: sold_status) }
  scope :overdue, -> { where("expected_return_datetime < ? AND drives.status = ?", Time.current, Drive.statuses[:in_progress]) }
  scope :by_salesperson, ->(uuid) { joins(:sales_person).where(users: { uuid: uuid }) }
  scope :by_customer, ->(uuid) { joins(:customer).where(customers: { uuid: uuid }) }
  scope :by_vehicle, ->(uuid) { joins(:vehicle).where(dealership_vehicles: { uuid: uuid }) }
  scope :by_trade_plate, ->(uuid) { joins(:trade_plate).where(trade_plates: { uuid: uuid }) }
  scope :start_datetime_between, ->(start_date = nil, end_date = nil) {
    query = all
    begin
      query = query.where(start_datetime: Date.parse(start_date).beginning_of_day..) if start_date.present?
      query = query.where(start_datetime: ..Date.parse(end_date).end_of_day) if end_date.present?
    rescue Date::Error, ArgumentError
      raise Errors::InvalidInput, "Invalid date format. Use YYYY-MM-DD format"
    end
    query
  }

  scope :eligible_for_return, -> {
    where(drive_type: VEHICLE_OUT_DRIVE_TYPES, status: :in_progress)
  }

  validate :validate_timestamps_not_in_future
  validate :validate_odometer_readings
  validate :validate_odometer_update_status, on: :update
  validate :validate_return_time, on: :update
  validate :validate_trade_plate_assignment, on: :update

  def cancel_with_reason!(reason)
    raise Errors::InvalidInput, "Cancel reason is required" if reason.blank?
    raise Errors::InvalidInput, "Cancellation not allowed" if completed? || cancelled?

    self.cancel_reason = reason
    self.cancelled_at = Time.current
    self.cancelled!
  end

  def to_param
    uuid
  end

  def mark_completed(notes = nil)
    raise Errors::InvalidInput, "This drive cannot be marked as completed" unless VEHICLE_OUT_DRIVE_TYPES.include? drive_type

    raise Errors::InvalidInput, "Only in-progress drives can be marked as completed" unless in_progress?

    self.notes = notes if notes.present?
    self.end_datetime = Time.current

    vehicle.last_known_odometer_km = end_odometer_reading
    if waypoints.present?
      location = GpsLocation.create!(
        latitude: waypoints.last.latitude,
        longitude: waypoints.last.longitude,
        accuracy: waypoints.last.accuracy,
        trackable: vehicle
      )
      vehicle.last_known_location.destroy!
      vehicle.last_known_location = location
    end
    if final_damage_report.present?
      report = DamageReport.create!(
        drive: nil,
        vehicle:,
        report_type: DamageReport::VEHICLE,
        description: final_damage_report.description
      )
      final_damage_report.media_files.each do |file|
        report.media_files.attach(io: file.download,
          filename: file.filename,
          content_type: file.content_type
          )
      end
      vehicle.last_damage_report.destroy!
      vehicle.last_damage_report = report
    end
    # last_known_fuel_gauge_level - TBD later
    vehicle.update!
    self.status = :completed
    self.update!
  end

  private

  def validate_timestamps_not_in_future
    now = Time.current

    if start_datetime.present? && start_datetime > now
      errors.add(:start_datetime, "cannot be in the future")
    end

    if end_datetime.present? && end_datetime > now
      errors.add(:end_datetime, "cannot be in the future")
    end
  end

  def validate_odometer_readings
    validate_odometer_positive_values
    validate_odometer_sequence
  end

  def validate_odometer_positive_values
    if start_odometer_reading.present? && start_odometer_reading.negative?
      errors.add(:start_odometer_reading, "must be a positive number")
    end

    if end_odometer_reading.present? && end_odometer_reading.negative?
      errors.add(:end_odometer_reading, "must be a positive number")
    end
  end

  def validate_odometer_sequence
    return unless start_odometer_reading.present? && end_odometer_reading.present?

    if start_odometer_reading > end_odometer_reading
      errors.add(:end_odometer_reading, :invalid_sequence, message: "must be greater than start odometer reading")
    end
  end

  def sales_person_belongs_to_dealership
    return unless sales_person && dealership

    unless sales_person.dealerships.include?(dealership)
      errors.add(:sales_person, "must belong to the vehicle's dealership")
    end
  end

  def validate_odometer_update_status
    return unless start_odometer_reading_changed? || end_odometer_reading_changed?

    unless VEHICLE_OUT_DRIVE_TYPES.include? drive_type
      errors.add(:base, "Odometer readings can only be updated for test drives, loans, and self loans")
      return
    end

    if start_odometer_reading_changed? && !scheduled?
      errors.add(:start_odometer_reading, "can only be updated when drive is in scheduled status")
    end

    if end_odometer_reading_changed? && !in_progress?
      errors.add(:end_odometer_reading, "can only be updated when drive is in progress")
    end
  end

  def validate_return_time
    return unless expected_return_datetime_changed?

    unless VEHICLE_OUT_DRIVE_TYPES.include?(drive_type) || BOOKING_TYPES.include?(drive_type)
      errors.add(:base, "Return time cannot be updated for this drive type")
    end
  end

  def validate_trade_plate_assignment
    return unless trade_plate_id_changed?

    unless VEHICLE_OUT_DRIVE_TYPES.include?(drive_type)
      errors.add(:base, "Trade plate can only be assigned to vehicle out drives")
    end

    if vehicle.rego.present?
      errors.add(:base, "Trade plate can only be assigned to vehicles with blank registration")
    end
  end
end
