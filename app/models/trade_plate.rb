class TradePlate < ApplicationRecord
  include HasUuid

  belongs_to :dealership
  has_many :drives, dependent: :nullify

  validates :number, presence: true
  validates :number, uniqueness: { scope: :dealership_id }

  enum :status, {
    active: 0,
    inactive: 1
  }, default: :active

  # Check if trade plate is expired
  def expired?
    return false if expiry.nil?
    expiry < Date.current
  end

  # Check if trade plate is currently being used by an in-progress drive
  def in_use?
    drives.where(status: :in_progress).exists?
  end
end
