class User < ApplicationRecord
  include HasPhoneNumber
  include HasUuid
  include UserAttributes

  enum :preferred_2fa, { sms: 1, email: 2, totp: 3 }, default: :email

  has_phone_number(field_name: :phone, required: true)
  validates :email, presence: true, uniqueness: { case_sensitive: false }, email_format: true
  validates :first_name, :last_name, :preferred_2fa, presence: true
  validates :otp, numericality: {
    only_integer: true, greater_than_or_equal_to: 100000, less_than_or_equal_to: 999999
  }, allow_nil: true
  validate :password_complexity
  validates :photo, content_type: [ "image/png", "image/jpeg" ], size: { less_than: 5.megabytes }

  # Include default devise modules. Others available are:
  # :confirmable, :lockable, :timeoutable, :trackable and :omniauthable

  devise :database_authenticatable,
         :recoverable, :rememberable, :validatable,
         :jwt_authenticatable, jwt_revocation_strategy: JwtDenylist

  has_one :driver_license, as: :holder, dependent: :destroy
  has_many :device_registrations, dependent: :destroy

  has_many :user_dealerships, dependent: :destroy
  has_many :dealerships, through: :user_dealerships
  has_many :drives, foreign_key: "sales_person_id"
  has_one_attached :photo

  before_validation :normalize_email

  validates :first_name, length: { minimum: 3, maximum: 30 }
  validates :last_name, length: { minimum: 3, maximum: 30 }

  enum :status, {
    inactive: 0,
    active: 1,
    disabled: 2,
    deleted: 3
  }, default: :active

  enum :user_type, {
    dealership_user: 0,
    staff: 1,
    super_admin: 2
  }, default: :dealership_user

  enum :preferred_language, {
    english: 0,
    spanish: 1
  }, default: :english

  enum :time_zone, {
    utc: "UTC",
    perth: "Perth",
    sydney: "Sydney",
    melbourne: "Melbourne",
    brisbane: "Brisbane",
    adelaide: "Adelaide"
  }, default: :utc

  scope :active, -> { where(status: :active) }
  scope :ordered_by_name, -> { order(:first_name, :last_name) }
  scope :sales_people, -> { joins(:user_dealerships).where(user_dealerships: { role: :sales_person }) }

  def dealership_role(dealership)
    user_dealerships.find_by(dealership: dealership)&.role
  end

  def dealership_admin?(dealership)
    dealership_role(dealership) == "dealership_admin"
  end

  def sales_person?(dealership)
    dealership_role(dealership) == "sales_person"
  end

  def password_complexity
    return if password.blank? || password =~ /(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9])(?=.*?[#?!@$%^&*-])/

    errors.add :password, "requirement not met. Please use: 1 uppercase, 1 lowercase, 1 digit and 1 special character"
  end

  def active_devices
    device_registrations.valid.order(created_at: :desc)
  end

  def to_param
    uuid
  end

  def add_photo(photo_obj)
    transaction do
      photo.purge if photo.attached?
      photo.attach(photo_obj)
      # photo.attach(key: "profile_photos/#{uuid}", io: photo_obj, filename: photo_obj.original_filename, content_type: photo_obj.content_type)
    end
  end

  def profile_photo_url
    return nil unless photo.attached?

    photo.url
  end

  def logout_all_devices
    device_registrations.active.each(&:invalidate!)
  end

  def logout_device(device_id)
    device = device_registrations.active.find_by(device_id: device_id)
    device&.invalidate!
  end

  # Clean up inactive devices older than specified days
  def cleanup_old_devices(days = 90)
    device_registrations.where(active: false)
                .where("logged_out_at < ?", days.days.ago)
                .destroy_all
  end

  # Add scope for users requiring password change
  scope :password_change_required, -> { where(password_change_required: true) }

  # Method to mark password as changed
  def mark_password_as_changed!
    update(password_change_required: false)
  end

  # Method to require password change
  def require_password_change!
    update(password_change_required: true)
    logout_all_devices
  end

  # Add scope for users requiring onboarding
  scope :incomplete_onboarding, -> { where(onboarding_completed: false) }

  # Method to mark onboarding complete
  def mark_onboarding_completed!
    update(onboarding_completed: true)
  end

  def available_2fa_methods
    methods = [ "sms", "email" ]
    methods << "totp" if totp_secret.present?
    methods
  end

  private

  def normalize_email
    self.email = email.downcase if email.present?
  end
end
