module Vehicle
  class BodyPartCondition < ApplicationRecord
    belongs_to :vehicle_condition, class_name: "Vehicle::VehicleCondition"
    has_many_attached :photos

    enum :part_name, {
      front_bumper: 0,
      front_fender_skirt: 1,
      front_panel: 2,
      left_front_headlamp: 3,
      right_front_headlamp: 4,
      bonnet: 5,
      left_front_fender: 6,
      right_front_fender: 7,
      left_front_tyre: 8,
      left_front_wheel: 9,
      right_front_tyre: 10,
      right_front_wheel: 11,
      front_windshield: 12,
      left_front_door: 13,
      left_front_window: 14,
      left_running_board: 15,
      left_rear_window: 16,
      left_rear_door: 17,
      left_rear_fender: 18,
      left_rear_tyre: 19,
      left_rear_wheel: 20,
      right_front_door: 21,
      right_front_window: 22,
      right_running_board: 23,
      right_rear_window: 24,
      right_rear_door: 25,
      right_rear_fender: 26,
      right_rear_tyre: 27,
      right_rear_wheel: 28,
      rear_windshield: 29,
      boot: 30,
      left_rear_headlamp: 31,
      right_rear_headlamp: 32,
      rear_grill: 33
    }

    enum :condition, {
      okay: 0,
      scratch: 1,
      chip: 2,
      dent: 3,
      hail: 4,
      damaged: 5,
      acceptable: 6,
      not_acceptable: 7
    }

    validates :part_name, presence: true
    validates :condition, presence: true
  end
end
