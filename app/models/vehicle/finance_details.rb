module Vehicle
  class FinanceDetails < ApplicationRecord
    include HasUuid

    # Associations
    belongs_to :customer_vehicle

    # Validations
    validates :current_repayment_amount, numericality: { greater_than: 0 }, allow_nil: true
    validates :terms_months, numericality: { greater_than: 0, only_integer: true }, allow_nil: true
    validates :interest_rate, numericality: { greater_than: 0, less_than_or_equal_to: 100 }, allow_nil: true
    validates :next_due_date, presence: true, if: :is_financed?
    validates :finance_company, presence: true, if: :is_financed?
    validates :payout_amount, numericality: { greater_than_or_equal_to: 0 }, allow_nil: true

    # Methods
    def next_payment_due?
      return false unless next_due_date.present?
      next_due_date <= Date.current
    end

    def days_until_next_payment
      return nil unless next_due_date.present?
      (next_due_date - Date.current).to_i
    end
  end
end
