class Api::V1::UserSerializer < Api::V1::BaseSerializer
  fields :uuid, :email, :name, :first_name, :last_name, :phone, :preferred_2fa,
             :two_factor_methods, :profile_photo_url, :user_type, :job_title,
             :password_change_required, :preferred_language, :external_id, :time_zone,
             :onboarding_completed, :dealership_role

  association :dealerships, blueprint: Api::V1::DealershipSerializer

  field :two_factor_methods do |user|
    user.available_2fa_methods
  end

  field(:dealership_role) do |user, params|
    user.dealership_role(params[:dealership]) if params && params[:dealership]
  end
end
