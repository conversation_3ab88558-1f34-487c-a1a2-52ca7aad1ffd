class Auth::TokenService
  ACCESS_TOKEN_EXPIRY = 1.day.freeze
  REFRESH_TOKEN_EXPIRY = 1.year.freeze
  TEMP_TOKEN_EXPIRY = 5.minutes.freeze
  ACCESS_TOKEN = "access".freeze
  REFRESH_TOKEN = "refresh".freeze
  TEMPORARY_TOKEN = "temporary".freeze

  def initialize(device = nil, user = nil)
    @device = device
    @device_id = device&.device_id
    @user = user || device&.user
  end

  def decode_token(token)
    begin
      decoded_token = JWT.decode(token, devise_token_secret, true)
      user_id = decoded_token[0]&.fetch("sub", nil)
      if decoded_token[0]["token_type"] == TEMPORARY_TOKEN
        # get latest token from cache and compare with provided token
        latest_token = Rails.cache.read("temp_token_#{user_id}")
        raise Errors::InvalidToken, "Invalid token" if latest_token && latest_token != token
      end
      User.find_by(id: user_id)
    rescue JWT::ExpiredSignature
      raise Errors::TokenExpired, "Token has expired"
    rescue JWT::DecodeError
      raise Errors::InvalidToken, "Invalid token"
    end
  end

  def generate_tokens(force_refresh = false, provided_refresh_token = nil)
    expiry_time = ACCESS_TOKEN_EXPIRY.from_now.to_i
    access_token = jwt_token(expiry_time, ACCESS_TOKEN)

    if force_refresh || provided_refresh_token.nil? || device.refresh_token.blank? || device.refresh_token_expires_at <= Time.current
      refresh_token_expiry = REFRESH_TOKEN_EXPIRY.from_now.to_i
      new_refresh_token = jwt_token(refresh_token_expiry, REFRESH_TOKEN)

      # Store refresh token hash for security
      device.update!(
        refresh_token: Digest::SHA256.hexdigest(new_refresh_token),
        refresh_token_expires_at: Time.at(refresh_token_expiry),
        last_activity_at: Time.current
      )
    end

    {
      access_token: access_token,
      refresh_token: force_refresh ? new_refresh_token : provided_refresh_token,
      expires_at: expiry_time
    }
  end

  def refresh_access_token!(provided_refresh_token)
    # Verify the provided refresh token
    provided_token_hash = Digest::SHA256.hexdigest(provided_refresh_token)

    unless device.refresh_token == provided_token_hash && device.refresh_token_expires_at > Time.current
      raise Errors::InvalidRefreshToken, "Invalid refresh token"
    end

    begin
      # Decode and verify refresh token
      decoded = JWT.decode(
        provided_refresh_token,
        devise_token_secret,
        true
      )
      payload = decoded[0]

      # Verify token belongs to this device
      unless payload["device_id"] == device_id && payload["token_type"] == REFRESH_TOKEN
       raise Errors::InvalidRefreshToken, "Token mismatch"
      end

      # Generate new tokens
      generate_tokens(false, provided_refresh_token)

    rescue JWT::ExpiredSignature
      # Refresh token expired, user needs to login again
      device.invalidate!
      raise Errors::RefreshTokenExpired, "Refresh token expired, please login again"
    rescue JWT::DecodeError
      raise Errors::InvalidRefreshToken, "Invalid refresh token format"
    rescue StandardError => e
      raise Errors::InternalError, "Token refresh failed: #{e.message}"
    end
  end

  def generate_temporary_token
    expiry_time = TEMP_TOKEN_EXPIRY.from_now.to_i

    token = jwt_token(expiry_time, TEMPORARY_TOKEN)

    # Store token temporarily
    Rails.cache.write("temp_token_#{user.id}", token, expires_in: TEMP_TOKEN_EXPIRY)

    [ token, expiry_time ]
  end

  def decode_and_verify_token(device_id, token)
    begin
      @device_id = device_id
      decoded_token = JWT.decode(token, devise_token_secret, true)

      @payload = decoded_token[0]
      validate_token_claims
      validate_device_session

      [ @user, @device ]

    rescue JWT::ExpiredSignature
      raise Errors::TokenExpired, "Access token expired, please refresh"
    rescue JWT::DecodeError
      raise Errors::InvalidToken, "Invalid token"
    end
  end

  private

  attr_reader :device, :device_id, :user

  def validate_token_claims
    # Verify this is an access token, not refresh token
    unless @payload["token_type"] == ACCESS_TOKEN
      raise Errors::InvalidToken, "Invalid token type"
    end

    # Verify device ID matches
    unless device_id == @payload["device_id"]
      raise Errors::InvalidDevice, "Invalid device"
    end
  end

  def validate_device_session
    user_id = @payload["sub"]

    # Verify user device is still active
    @device = DeviceRegistration.valid.find_by(
      user_id: user_id,
      device_id: device_id
    )

    unless @device
      raise Errors::InvalidSession, "Device session invalid"
    end

    begin
      @user = User.find(user_id)
    rescue ActiveRecord::RecordNotFound
      raise Errors::UserNotFound, "User not found"
    end

    # Update last activity
    @device.update_column(:last_activity_at, Time.current)
  end

  def devise_token_secret
    Rails.application.credentials.devise_jwt_secret_key!
  end

  def jwt_token(expiry_time, token_type)
    payload = {
      sub: user.id.to_s,
      scp: "user",
      jti: SecureRandom.uuid,
      device_id: device_id,
      exp: expiry_time,
      token_type: token_type,
      issued_at: Time.current.to_i
    }
    JWT.encode(payload, devise_token_secret)
  end
end
