require "rotp"
require "rqrcode"
class OtpService < BaseCodeSendService
  ISSUER = "DealerDrive"

  def generate_and_send_otp(channel = "email", action = :login)
    return nil if channel == "totp"

    check_rate_limit!

    @otp = generate_code
    record_code!(@otp)
    send_otp_via_channel(channel, action)

    @otp
  end

  def validate_otp(input_otp, channel = "email")
    if channel == "totp"
      return verify_with_authenticator(input_otp)
    end

    validate_code_with_expiry(user.otp, input_otp, user.otp_generated_at)
  end

  def clear_code!
    user.update!(otp: nil, otp_generated_at: nil, otp_resend_count: 0)
  end

  def record_code!(code)
    user.update!(
      otp: code,
      otp_generated_at: Time.current,
      otp_resend_count: user.otp_resend_count + 1
    )
  end

  def send_otp_via_channel(channel, action)
    case channel
    when "email"
      send_otp_via_email(action)
    when "sms"
      send_otp_via_sms(action)
    end
  end

  def send_otp_via_sms(action)
    message = I18n.t("sms.#{action}.message", otp: @otp, issuer: ISSUER)
    SmsJob.perform_later(to: user.phone, message: message)
  end

  def send_otp_via_email(action)
    UserMailer.send_otp(user, @otp, action).deliver_later
  end

  def verify_with_authenticator(otp)
    return false if otp.blank?

    # Only verify with permanently stored TOTP secret
    return false unless user.totp_secret.present?

    totp = ROTP::TOTP.new(user.totp_secret, issuer: ISSUER)
    totp.verify(otp, drift_behind: 15)
  end

  def setup_totp
    # Generate a random secret using ROTP's built-in method
    secret = ROTP::Base32.random
    totp = ROTP::TOTP.new(secret, issuer: ISSUER)
    provisioning_uri = totp.provisioning_uri(user.email)
    qr = RQRCode::QRCode.new(provisioning_uri)
    qr_png = qr.as_png(
      bit_depth: 1,
      border_modules: 4,
      color_mode: ChunkyPNG::COLOR_GRAYSCALE,
      color: "black",
      file: nil,
      fill: "white",
      module_px_size: 6,
      resize_exactly_to: false,
      resize_gte_to: false,
      size: 240
    )

    # Store secret temporarily
    Rails.cache.write("totp_setup_#{user.id}", secret, expires_in: 10.minutes)

    {
      secret_key: secret,
      qr_code: Base64.strict_encode64(qr_png.to_s),
      qr_code_url: provisioning_uri
    }
  rescue StandardError => e
    raise Errors::AuthenticationError, "2FA setup failed: #{e.message}"
  end

  def verify_totp_setup(otp_code)
    secret = Rails.cache.read("totp_setup_#{user.id}")
    raise Errors::InvalidInput, "2FA setup session expired. Please start setup again." unless secret

    totp = ROTP::TOTP.new(secret, issuer: ISSUER)
    if totp.verify(otp_code, drift_behind: 15)
      # Save the secret and enable 2FA
      user.update!(
        totp_secret: secret,
        preferred_2fa: "totp"
      )

      # Clear the temporary secret from cache
      Rails.cache.delete("totp_setup_#{user.id}")
    else
      raise Errors::InvalidInput, "Invalid OTP code"
    end
  end

  protected

  def resend_count
    user.otp_resend_count
  end

  def code_generated_at
    user.otp_generated_at
  end
end
