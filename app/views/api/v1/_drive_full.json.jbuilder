json.extract! drive, :uuid, :drive_type, :status, :sold_status, :notes, :start_odometer_reading, :end_odometer_reading

json.cancel_reason drive.cancel_reason if drive.cancel_reason
json.cancelled_at format_iso8601_with_offset(drive.cancelled_at) if drive.cancelled_at
json.expected_pickup_datetime format_iso8601_with_offset(drive.expected_pickup_datetime) if drive.expected_pickup_datetime
json.expected_return_datetime format_iso8601_with_offset(drive.expected_return_datetime) if drive.expected_return_datetime
json.start_datetime format_iso8601_with_offset(drive.start_datetime) if drive.start_datetime
json.end_datetime format_iso8601_with_offset(drive.end_datetime) if drive.end_datetime

json.vehicle do
  json.extract! drive.vehicle, :uuid, :make, :model, :build_year, :vin, :rego, :color, :display_name, :status, :vehicle_type
  json.extract! drive.vehicle, :stock_number, :rego_expiry, :is_trade_plate_used, :available_for_drive
  json.photo_urls drive.vehicle.photos.map { |photo| photo.url } if drive.vehicle.photos.attached?
  if drive.vehicle.brand
    json.brand do
      json.extract! drive.vehicle.brand, :uuid, :name, :logo_url
    end
  end
end

if drive.customer
  json.customer do
    json.extract! drive.customer, :uuid, :first_name, :last_name, :email, :phone_number, :full_name, :gender, :age,
                                  :company_name, :external_id, :postcode, :suburb, :address_line1, :address_line2, :city, :state, :country

    if drive.customer.driver_license
      json.driver_license do
        json.extract! drive.customer.driver_license, :uuid, :licence_number, :expiry_date, :issuing_state, :issuing_country, :category,
                                                     :full_name, :date_of_birth, :front_image_url, :back_image_url, :verification_status,
                                                     :issue_date, :verification_rejection_reason
      end
    end
  end
end

if drive.initial_damage_report
  json.initial_damage_report do
    json.extract! drive.initial_damage_report, :uuid, :description, :report_type
    json.media_files_count drive.initial_damage_report.media_files.count
    json.media_files do
      json.array! drive.initial_damage_report.media_files do |file|
        json.filename file.filename.to_s
        json.content_type file.content_type
        json.byte_size file.byte_size
        json.url file&.url
      end
    end
    json.created_at format_iso8601_with_offset(drive.initial_damage_report.created_at)
    json.updated_at format_iso8601_with_offset(drive.initial_damage_report.updated_at)
  end
end

if drive.final_damage_report
  json.final_damage_report do
    json.extract! drive.final_damage_report, :uuid, :description, :report_type
    json.media_files_count drive.final_damage_report.media_files.count
    json.media_files do
      json.array! drive.final_damage_report.media_files do |file|
        json.filename file.filename.to_s
        json.content_type file.content_type
        json.byte_size file.byte_size
        json.url file&.url
      end
    end
    json.created_at format_iso8601_with_offset(drive.final_damage_report.created_at)
    json.updated_at format_iso8601_with_offset(drive.final_damage_report.updated_at)
  end
end

json.sales_person do
  json.extract! drive.sales_person, :uuid, :first_name, :last_name, :email, :full_name, :phone, :job_title
end

if drive.sales_person_accompanying
    json.sales_person_accompanying do
      json.extract! drive.sales_person_accompanying, :uuid, :first_name, :last_name, :email, :full_name, :phone, :job_title
  end
end

json.waypoints do
  json.array! drive.waypoints do |waypoint|
    json.extract! waypoint, :latitude, :longitude, :accuracy
    json.created_at format_iso8601_with_offset(waypoint.created_at)
  end
end

if drive.trade_plate
  json.trade_plate do
    json.extract! drive.trade_plate, :uuid, :number, :expiry, :status
  end
end

json.created_at format_iso8601_with_offset(drive.created_at)
json.updated_at format_iso8601_with_offset(drive.updated_at)
