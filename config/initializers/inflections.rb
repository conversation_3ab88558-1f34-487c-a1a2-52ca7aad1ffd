# Be sure to restart your server when you modify this file.

# Add new inflection rules using the following format. Inflections
# are locale specific, and you may define rules for as many different
# locales as you wish. All of these examples are active by default:
ActiveSupport::Inflector.inflections(:en) do |inflect|
  #   inflect.plural /^(ox)$/i, "\\1en"
  #   inflect.singular /^(ox)en/i, "\\1"
  inflect.irregular "drive", "drives"
  inflect.irregular "finance_detail", "finance_details"
  inflect.irregular "vehicle_condition", "vehicle_conditions"
  inflect.irregular "options_fitted", "options_fitted"
  inflect.irregular "component_rating", "component_ratings"
  inflect.irregular "body_part_condition", "body_part_conditions"
  inflect.irregular "reconditioning_cost", "reconditioning_costs"
  #   inflect.uncountable %w( fish sheep )
end

# These inflection rules are supported but not enabled by default:
# ActiveSupport::Inflector.inflections(:en) do |inflect|
#   inflect.acronym "RESTful"
# end
