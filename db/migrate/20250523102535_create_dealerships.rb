class CreateDealerships < ActiveRecord::Migration[8.0]
  def change
    create_table :dealerships do |t|
      t.string :name, null: false
      t.integer :status, null: false, default: 0
      t.string :address_line1, null: false
      t.string :address_line2
      t.string :suburb
      t.string :state, null: false
      t.string :postcode, null: false
      t.string :country, null: false, default: 'au'
      t.string :telephone, null: false
      t.string :email, null: false
      t.integer :setting_date_format, null: false, default: 0
      t.string :setting_time_zone, null: false, default: 'UTC'
      t.integer :setting_distance_unit, null: false, default: 0
      t.string :external_id
      t.string :abn

      t.timestamps
    end

    add_index :dealerships, :name
    add_index :dealerships, :setting_time_zone
    add_index :dealerships, :external_id
  end
end
