  require 'securerandom'

class AddsUuidToVehicleRelatedModels < ActiveRecord::Migration[8.0]
  def change
    add_column :customers, :uuid, :string, limit: 36
    Customer.reset_column_information
    Customer.find_each { |customer| customer.update_columns(uuid: SecureRandom.uuid) }
    add_index :customers, :uuid, unique: true

   add_column :damage_reports, :uuid, :string, limit: 36
   DamageReport.reset_column_information
   DamageReport.find_each { |damage_report| damage_report.update_columns(uuid: SecureRandom.uuid) }
   add_index :damage_reports, :uuid, unique: true

   add_column :drives, :uuid, :string, limit: 36
   Drive.reset_column_information
   Drive.find_each { |drive| drive.update_columns(uuid: SecureRandom.uuid) }
   add_index :drives, :uuid, unique: true

   add_column :driver_licenses, :uuid, :string, limit: 36
   DriverLicense.reset_column_information
   DriverLicense.find_each { |driver_license| driver_license.update_columns(uuid: SecureRandom.uuid) }
   add_index :driver_licenses, :uuid, unique: true

   add_column :vehicles, :uuid, :string, limit: 36
   #  Vehicle.reset_column_information
   #  Vehicle.find_each { |vehicle| vehicle.update_columns(uuid: SecureRandom.uuid) }
   add_index :vehicles, :uuid, unique: true
  end
end
