class AddFieldsToUser < ActiveRecord::Migration[8.0]
  def change
    add_column :users, :status, :integer, limit: 1, null: false, default: 0, unsigned: true
    add_column :users, :user_type, :integer, limit: 1, null: false, default: 0, unsigned: true
    add_column :users, :last_login_ip, :string
    add_column :users, :last_login_timestamp, :datetime
    add_column :users, :job_title, :string
    add_column :users, :delete_reason, :string
    add_column :users, :preferred_language, :integer, limit: 1, null: false, default: 0, unsigned: true
    add_column :users, :external_id, :string
    add_column :users, :time_zone, :string, null: false, default: 'UTC'

    add_index :users, :external_id
  end
end
