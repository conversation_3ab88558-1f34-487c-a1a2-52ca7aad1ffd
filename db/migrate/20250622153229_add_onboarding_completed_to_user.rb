class AddOnboardingCompletedToUser < ActiveRecord::Migration[8.0]
  def change
    add_column :users, :onboarding_completed, :boolean, default: false, null: false

    add_column :device_registrations, :uuid, :string, limit: 36
    DeviceRegistration.reset_column_information
    DeviceRegistration.find_each { |device| device.update_columns(uuid: SecureRandom.uuid) }
    add_index :device_registrations, :uuid, unique: true
  end
end
