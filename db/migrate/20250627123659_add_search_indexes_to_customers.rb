class AddSearchIndexesToCustomers < ActiveRecord::Migration[8.0]
  def change
    add_index :customers, :first_name
    add_index :customers, :last_name
    add_index :customers, :email
    add_index :customers, :phone_number

    add_index :customers, [ :first_name, :last_name ], name: 'index_customers_on_full_name'

    add_index :customers, [ :dealership_id, :first_name ], name: 'index_customers_on_dealership_first_name'
    add_index :customers, [ :dealership_id, :last_name ], name: 'index_customers_on_dealership_last_name'
    add_index :customers, [ :dealership_id, :email ], name: 'index_customers_on_dealership_email'
    add_index :customers, [ :dealership_id, :phone_number ], name: 'index_customers_on_dealership_phone_number'
  end
end
