class CreateCustomerVehicles < ActiveRecord::Migration[8.0]
  def change
    create_table :customer_vehicles do |t|
      # UUID for the model
      t.string :uuid, limit: 36, null: false

      # Associations
      t.references :customer, null: false, foreign_key: true
      t.references :dealership, null: false, foreign_key: true

      # VehicleBase fields (from VehicleBase concern)
      t.string :make, null: false, limit: 255
      t.string :model, null: false, limit: 255
      t.string :rego, limit: 20
      t.string :vin, limit: 17
      t.integer :build_year, null: false
      t.integer :build_month

      # CustomerVehicle specific fields (from API specification and model)
      t.boolean :is_vehicle_present, default: true
      t.string :registration_state, limit: 10
      t.integer :compliance_month
      t.integer :compliance_year
      t.date :odometer_date
      t.integer :odometer_reading
      t.string :exterior_color, limit: 45
      t.string :interior_color, limit: 45
      t.integer :seat_type, limit: 1, default: 0 # enum: leather: 0, cloth: 1, mixed: 2
      t.date :registration_expiry
      t.string :engine_size, limit: 20
      t.string :engine_number, limit: 50
      t.integer :fuel_type, limit: 1, default: 0 # enum: petrol: 0, diesel: 1, electric: 2, hybrid: 3, plugin_hybrid: 4, lpg: 5, other: 6
      t.integer :driving_wheels, limit: 1, default: 0 # enum: fwd: 0, rwd: 1, awd: 2, four_wd: 3
      t.string :wheel_size, limit: 20
      t.integer :spare_wheel_type, limit: 1, default: 0 # enum: full_size: 0, space_saver: 1, run_flat: 2, repair_kit: 3, none: 4
      t.integer :transmission, limit: 1, default: 0 # enum: manual: 0, automatic: 1, cvt: 2, semi_automatic: 3, dual_clutch: 4
      t.integer :number_of_doors, limit: 2
      t.integer :number_of_seats, limit: 2
      t.integer :body_type, limit: 1, default: 0 # enum: sedan: 0, hatchback: 1, wagon: 2, suv: 3, coupe: 4, convertible: 5, ute: 6, van: 7, truck: 8, other: 9
      t.string :redbook_code, limit: 50
      t.references :brand, null: true, foreign_key: true

      t.timestamps

      # Indexes
      t.index :uuid, unique: true
      t.index [ :customer_id, :rego ], name: 'index_customer_vehicles_on_customer_rego'
      t.index :rego, name: 'index_customer_vehicles_on_rego'
      t.index :vin, name: 'index_customer_vehicles_on_vin'
      t.index [ :dealership_id, :make, :model ], name: 'index_customer_vehicles_on_dealership_make_model'
      t.index :is_vehicle_present, name: 'index_customer_vehicles_on_vehicle_present'
    end
  end
end
