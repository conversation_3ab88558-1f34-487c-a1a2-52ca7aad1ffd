class CreateOptionsFitted < ActiveRecord::Migration[8.0]
  def change
    create_table :options_fitted do |t|
      t.string :uuid, limit: 36, null: false
      t.references :customer_vehicle, null: false, foreign_key: true, index: { unique: true }
      t.boolean :has_sunroof, default: false
      t.boolean :has_tinted_windows, default: false
      t.boolean :has_towbar, default: false
      t.boolean :has_keyless_entry, default: false
      t.boolean :has_bluetooth, default: false
      t.boolean :has_ventilated_seats, default: false
      t.boolean :has_tray_fitted, default: false
      t.boolean :has_canopy_fitted, default: false
      t.boolean :has_aftermarket_wheels, default: false
      t.boolean :has_bull_bar, default: false
      t.boolean :has_extended_warranty, default: false
      t.date :extended_warranty_expiry
      t.boolean :ppsr, default: false
      t.json :additional_options

      t.timestamps
    end

    add_index :options_fitted, :uuid, unique: true
  end
end
