puts "Seeding drives..."

unless Dealership.any?
  puts "Dealerships missing. Please run dealership seeds first."
  return
end

unless Customer.any?
  puts "Customers missing. Please run customer seeds first."
  return
end

unless DealershipVehicle.any?
  puts "Vehicles missing. Please run vehicle seeds first."
  return
end

unless User.sales_people.any?
  puts "Sales People missing. Please run user seeds first."
  return
end

dealership1 = Dealership.first
dealership2 = Dealership.second

[
  {
    dealership: dealership1,
    drive_type: :test_drive_booking,
    status: :scheduled,
    expected_pickup_datetime: 1.day.from_now.change(hour: 10),
    expected_return_datetime: 1.day.from_now.change(hour: 12),
    notes: "Test Drive Booking 1"
  },
  {
    dealership: dealership1,
    drive_type: :loan_booking,
    status: :scheduled,
    expected_pickup_datetime: 1.day.from_now.change(hour: 10),
    expected_return_datetime: 2.days.from_now.change(hour: 12),
    notes: "Loan Booking 1"
  },
  {
    dealership: dealership1,
    drive_type: :enquiry,
    notes: "Enquiry 1"
  },
  {
    dealership: dealership1,
    drive_type: :test_drive_booking,
    status: :cancelled,
    cancel_reason: "Customer changed mind",
    cancelled_at: 1.day.ago,
    notes: "Cancelled Test Drive Booking 1"
  },
  {
    dealership: dealership1,
    drive_type: :test_drive,
    status: :in_progress,
    start_odometer_reading: 1000,
    sales_person_accompanying_id: dealership1.sales_people.first.id,
    notes: "Test Drive 1",
    start_datetime: 1.day.ago.change(hour: 10)
  },
  {
    dealership: dealership1,
    drive_type: :test_drive,
    status: :completed,
    start_odometer_reading: 10000,
    end_odometer_reading: 10100,
    sold_status: :sold,
    notes: "Completed Test Drive 1 Sold",
    start_datetime: 2.days.ago.change(hour: 10),
    end_datetime: 2.days.ago.change(hour: 12)
  },
  {
    dealership: dealership1,
    drive_type: :loan,
    status: :in_progress,
    with_trade_plate: true,
    start_odometer_reading: 20000,
    notes: "Loan 1",
    start_datetime: 1.day.ago.change(hour: 10)
  },
  {
    dealership: dealership1,
    drive_type: :loan,
    status: :completed,
    start_odometer_reading: 30000,
    end_odometer_reading: 30500,
    notes: "Completed Loan 1",
    start_datetime: 2.days.ago.change(hour: 10),
    end_datetime: 1.day.ago.change(hour: 12)
  },
  {
    dealership: dealership1,
    drive_type: :self_loan,
    status: :completed,
    start_odometer_reading: 40000,
    end_odometer_reading: 40500,
    notes: "Completed Self Loan 1",
    start_datetime: 2.days.ago.change(hour: 10),
    end_datetime: 1.day.ago.change(hour: 12)
  },
  {
    dealership: dealership2,
    drive_type: :test_drive_booking,
    status: :scheduled,
    start_odometer_reading: nil
  },
  {
    dealership: dealership2,
    drive_type: :test_drive_booking,
    status: :scheduled,
    start_odometer_reading: nil
  }
].each_with_index do |drive_data, i|
  dealership = drive_data[:dealership]
  vehicle = dealership.vehicles.order("RAND()").first
  vehicle = dealership.vehicles.where(rego: nil).order("RAND()").first if drive_data[:with_trade_plate]
  customer = dealership.customers.order("RAND()").first
  sales_person = dealership.sales_people.order("RAND()").first
  trade_plate = dealership.trade_plates.active.order("RAND()").first if drive_data[:with_trade_plate]

  if vehicle && customer && sales_person
    Drive.create!(
      dealership: dealership,
      vehicle: vehicle,
      customer: customer,
      trade_plate: trade_plate,
      sales_person: sales_person,
      **drive_data.except(:dealership, :with_trade_plate)
    )
    puts "Created drive for #{customer.full_name} in #{dealership.name} and Trade plate: #{trade_plate&.number}"
  else
    puts "Skipping: Missing data for drive #{i + 1}"
  end
end

puts "Drives seeded successfully!"
