namespace :auth do
  desc "Clean up expired tokens and old inactive devices"
  task cleanup: :environment do
    # Clean up expired JWT tokens from denylist
    expired_tokens = JwtDenylist.where("exp < ?", Time.current).count
    JwtDenylist.where("exp < ?", Time.current).delete_all

    # Clean up old inactive devices (older than 3 months)
    old_devices = DeviceRegistration.where(active: false)
                           .where("logged_out_at < ?", 3.months.ago)
                           .count
    DeviceRegistration.where(active: false)
              .where("logged_out_at < ?", 3.months.ago)
              .delete_all

    # Clean up devices that haven't been active for 6 months
    stale_devices = DeviceRegistration.where(active: true)
                             .where("last_activity_at < ?", 6.months.ago)
                             .count
    DeviceRegistration.where(active: true)
              .where("last_activity_at < ?", 6.months.ago)
              .update_all(active: false, logged_out_at: Time.current)

    puts "Cleaned up #{expired_tokens} expired JWT tokens"
    puts "Removed #{old_devices} old inactive device records"
    puts "Deactivated #{stale_devices} stale devices"
  end
end
