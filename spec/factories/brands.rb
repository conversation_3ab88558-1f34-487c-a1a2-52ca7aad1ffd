FactoryBot.define do
  factory :brand do
    sequence(:name) { |n| "Brand #{n}" }

    trait :with_logo do
      after(:build) do |brand|
        brand.logo.attach(
          io: File.open(Rails.root.join("spec", "fixtures", "files", "test_logo.png")),
          filename: "test_logo.png",
          content_type: "image/png"
        )
      end
    end

    trait :without_logo do
      logo { nil }
    end

    trait :with_invalid_logo_type do
      after(:build) do |brand|
        brand.logo.attach(
          io: StringIO.new("some text content"),
          filename: "test.txt",
          content_type: "text/plain"
        )
      end
    end

    trait :with_large_logo do
      after(:build) do |brand|
        # Create a 6MB string of random data
        large_content = SecureRandom.bytes(6.megabytes)
        brand.logo.attach(
          io: StringIO.new(large_content),
          filename: "large_logo.png",
          content_type: "image/png"
        )
      end
    end
  end
end
