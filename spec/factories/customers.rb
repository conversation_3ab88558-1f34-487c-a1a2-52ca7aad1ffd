FactoryBot.define do
  factory :customer do
    dealership
    first_name { Faker::Name.first_name }
    last_name { Faker::Name.last_name }
    age { Faker::Number.between(from: 18, to: 80) }
    phone_number { Faker::PhoneNumber.phone_number }
    email { Faker::Internet.email }
    gender { Customer.genders.keys.sample }
    postcode { Faker::Address.postcode }
    suburb { Faker::Address.community }
    address_line1 { Faker::Address.street_address }
    address_line2 { Faker::Address.secondary_address }
    city { Faker::Address.city }
    state { Faker::Address.state }
    country { Faker::Address.country }
  end
end
