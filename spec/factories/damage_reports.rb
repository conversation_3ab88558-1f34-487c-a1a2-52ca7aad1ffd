# frozen_string_literal: true

FactoryBot.define do
  factory :damage_report do
    description { Faker::Lorem.paragraph }
    vehicle

    trait :with_initial_drive do
      drive
      report_type { :initial }
    end

    trait :with_final_drive do
      drive
      report_type { :final }
    end

    trait :with_media_files do
      after(:build) do |damage_report|
        damage_report.media_files.attach([
          {
            io: File.open(Rails.root.join("spec", "fixtures", "files", "car_1.png")),
            filename: "car_1.png",
            content_type: "image/png"
          },
          {
            io: File.open(Rails.root.join("spec", "fixtures", "files", "car_2.jpg")),
            filename: "car_2.jpg",
            content_type: "image/jpeg"
          },
          {
            io: File.open(Rails.root.join("spec", "fixtures", "files", "short_video.mp4")),
            filename: "short_video.mp4",
            content_type: "video/mp4"
          }
        ])
      end
    end

    trait :with_invalid_media_files do
      after(:build) do |damage_report|
        damage_report.media_files.attach(
          {
            io: StringIO.new("some text content"),
            filename: "test.txt",
            content_type: "text/plain"
          }
        )
      end
    end
  end
end
