FactoryBot.define do
  factory :dealership_features_setting do
    association :dealership
    advance_booking_enabled { false }
    insurance_waiver_enabled { false }
    dealer_drive_subscription { false }
    appraisals_subscription { false }
    setting_recent_customer_age { nil }

    trait :with_advance_booking_enabled do
      advance_booking_enabled { true }
    end

    trait :with_advance_booking_disabled do
      advance_booking_enabled { false }
    end

    trait :with_insurance_waiver_enabled do
      insurance_waiver_enabled { true }
    end

    trait :with_insurance_waiver_disabled do
      insurance_waiver_enabled { false }
    end

    trait :with_dealer_drive_subscription_enabled do
      dealer_drive_subscription { true }
    end

    trait :with_dealer_drive_subscription_disabled do
      dealer_drive_subscription { false }
    end

    trait :with_appraisals_subscription_enabled do
      appraisals_subscription { true }
    end

    trait :with_appraisals_subscription_disabled do
      appraisals_subscription { false }
    end

    trait :with_recent_customer_age do
      setting_recent_customer_age { 30 }
    end
  end
end
