FactoryBot.define do
  factory :options_fitted, class: 'Vehicle::OptionsFitted' do
    association :customer_vehicle

    # Default boolean values (all false by default)
    has_sunroof { false }
    has_tinted_windows { false }
    has_towbar { false }
    has_keyless_entry { false }
    has_bluetooth { false }
    has_ventilated_seats { false }
    has_tray_fitted { false }
    has_canopy_fitted { false }
    has_aftermarket_wheels { false }
    has_bull_bar { false }
    has_extended_warranty { false }
    ppsr { false }

    # Extended warranty expiry (nil by default)
    extended_warranty_expiry { nil }

    # Additional options (empty hash by default)
    additional_options { {} }

    # New fields
    sunroof_type { :standard_metal }
    number_of_keys { 2 }
    heated_seats { false }
    cargo_blind { false }
    tonneau_cover { false }
    tonneau_type { :hard }
    on_written_off_register { :unknown }
    last_ppsr_date { nil }
    # options_images will be attached in specs as needed

    trait :with_extended_warranty do
      has_extended_warranty { true }
      extended_warranty_expiry { 1.year.from_now.to_date }
    end

    trait :with_ppsr do
      ppsr { true }
    end

    trait :with_sunroof do
      has_sunroof { true }
    end

    trait :with_tinted_windows do
      has_tinted_windows { true }
    end

    trait :with_towbar do
      has_towbar { true }
    end

    trait :with_keyless_entry do
      has_keyless_entry { true }
    end

    trait :with_bluetooth do
      has_bluetooth { true }
    end

    trait :with_ventilated_seats do
      has_ventilated_seats { true }
    end

    trait :with_tray_fitted do
      has_tray_fitted { true }
    end

    trait :with_canopy_fitted do
      has_canopy_fitted { true }
    end

    trait :with_aftermarket_wheels do
      has_aftermarket_wheels { true }
    end

    trait :with_bull_bar do
      has_bull_bar { true }
    end

    trait :with_additional_options do
      additional_options do
        {
          "custom_paint" => "Metallic Blue",
          "performance_exhaust" => true,
          "sports_suspension" => true,
          "custom_interior" => "Leather with red stitching"
        }
      end
    end

    trait :fully_loaded do
      has_sunroof { true }
      has_tinted_windows { true }
      has_towbar { true }
      has_keyless_entry { true }
      has_bluetooth { true }
      has_ventilated_seats { true }
      has_tray_fitted { true }
      has_canopy_fitted { true }
      has_aftermarket_wheels { true }
      has_bull_bar { true }
      has_extended_warranty { true }
      extended_warranty_expiry { 2.years.from_now.to_date }
      ppsr { true }
      additional_options do
        {
          "premium_sound_system" => true,
          "navigation_system" => true,
          "parking_sensors" => true,
          "reverse_camera" => true
        }
      end
    end

    trait :expired_warranty do
      has_extended_warranty { true }
      extended_warranty_expiry { 1.month.ago.to_date }
    end

    trait :expiring_warranty do
      has_extended_warranty { true }
      extended_warranty_expiry { 1.month.from_now.to_date }
    end

    trait :with_sunroof_type_standard_glass do
      sunroof_type { :standard_glass }
    end
    trait :with_sunroof_type_panoramic do
      sunroof_type { :panoramic }
    end
    trait :with_one_key do
      number_of_keys { 1 }
    end
    trait :with_three_keys do
      number_of_keys { 3 }
    end
    trait :with_heated_seats do
      heated_seats { true }
    end
    trait :with_cargo_blind do
      cargo_blind { true }
    end
    trait :with_tonneau_cover do
      tonneau_cover { true }
    end
    trait :with_tonneau_type_soft do
      tonneau_type { :soft }
    end
    trait :on_written_off_register_yes do
      on_written_off_register { :yes }
    end
    trait :on_written_off_register_no do
      on_written_off_register { :no }
    end
    trait :with_last_ppsr_date do
      last_ppsr_date { Date.today }
    end
  end
end
