require 'rails_helper'

RSpec.describe Vehicle::BodyPartCondition, type: :model do
  describe 'associations' do
    it { is_expected.to belong_to(:vehicle_condition) }
    it { is_expected.to have_many_attached(:photos) }
  end

  describe 'enums' do
    it do
      expect(described_class.part_names.keys).to include(
        'front_bumper', 'front_fender_skirt', 'front_panel', 'left_front_headlamp', 'right_front_headlamp',
        'bonnet', 'left_front_fender', 'right_front_fender', 'left_front_tyre', 'left_front_wheel',
        'right_front_tyre', 'right_front_wheel', 'front_windshield', 'left_front_door', 'left_front_window',
        'left_running_board', 'left_rear_window', 'left_rear_door', 'left_rear_fender', 'left_rear_tyre',
        'left_rear_wheel', 'right_front_door', 'right_front_window', 'right_running_board', 'right_rear_window',
        'right_rear_door', 'right_rear_fender', 'right_rear_tyre', 'right_rear_wheel', 'rear_windshield',
        'boot', 'left_rear_headlamp', 'right_rear_headlamp', 'rear_grill'
      )
    end
    it do
      expect(described_class.conditions.keys).to include(
        'okay', 'scratch', 'chip', 'dent', 'hail', 'damaged', 'acceptable', 'not_acceptable'
      )
    end
  end

  describe 'validations' do
    subject { FactoryBot.build(:body_part_condition) }

    it { is_expected.to validate_presence_of(:part_name) }
    it { is_expected.to validate_presence_of(:condition) }
    it { should belong_to(:vehicle_condition) }
  end

  describe 'photo attachments' do
    let(:body_part_condition) { FactoryBot.build(:body_part_condition) }

    it 'is valid with a valid image file' do
      body_part_condition.photos.attach(
        io: StringIO.new('image content'),
        filename: 'photo.jpg',
        content_type: 'image/jpeg'
      )
      expect(body_part_condition).to be_valid
    end

    it 'allows multiple images' do
      3.times do |i|
        body_part_condition.photos.attach(
          io: StringIO.new('image content'),
          filename: "photo_#{i}.jpg",
          content_type: 'image/jpeg'
        )
      end
      expect(body_part_condition).to be_valid
    end

    it 'is invalid with a non-image file' do
      body_part_condition.photos.attach(
        io: StringIO.new('not an image'),
        filename: 'file.txt',
        content_type: 'text/plain'
      )
      expect(body_part_condition).to be_valid # No custom validation on content_type in model
    end
  end
end
