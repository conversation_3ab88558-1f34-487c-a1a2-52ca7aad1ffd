require 'rails_helper'

RSpec.describe DriverLicense, type: :model do
  describe 'associations' do
    it { is_expected.to belong_to(:holder) }
  end

  describe 'validations' do
    subject { build(:driver_license) }
    it { is_expected.to validate_presence_of(:licence_number) }
    it { is_expected.to validate_presence_of(:expiry_date) }

    describe 'image validations' do
      let(:driver_license) { create(:driver_license) }

      context 'when front image is attached' do
        it 'validates content type' do
          driver_license.front_image.attach(
            io: StringIO.new('some text content'),
            filename: 'test.txt',
            content_type: 'text/plain'
          )
          expect(driver_license).not_to be_valid
          expect(driver_license.errors[:front_image]).to include('has an invalid content type (authorized content types are PNG, JPG)')
        end

        it 'validates file size' do
          large_content = SecureRandom.bytes(6.megabytes)
          driver_license.front_image.attach(
            io: StringIO.new(large_content),
            filename: 'large.png',
            content_type: 'image/png'
          )
          expect(driver_license).not_to be_valid
          expect(driver_license.errors[:front_image]).to include('file size must be less than 5 MB (current size is 6 MB)')
        end
      end

      context 'when back image is attached' do
        it 'validates content type' do
          driver_license.back_image.attach(
            io: StringIO.new('some text content'),
            filename: 'test.txt',
            content_type: 'text/plain'
          )
          expect(driver_license).not_to be_valid
          expect(driver_license.errors[:back_image]).to include('has an invalid content type (authorized content types are PNG, JPG)')
        end

        it 'validates file size' do
          large_content = SecureRandom.bytes(6.megabytes)
          driver_license.back_image.attach(
            io: StringIO.new(large_content),
            filename: 'large.png',
            content_type: 'image/png'
          )
          expect(driver_license).not_to be_valid
          expect(driver_license.errors[:back_image]).to include('file size must be less than 5 MB (current size is 6 MB)')
        end
      end
    end
  end

  describe 'enums' do
    it { is_expected.to define_enum_for(:verification_status).with_values(pending: 0, verified: 1, rejected: 2).backed_by_column_of_type(:integer) }

    describe 'default values' do
      it 'sets default verification_status to pending' do
        driver_license = DriverLicense.new
        expect(driver_license.verification_status).to eq('pending')
      end
    end
  end

  describe 'image types' do
    describe '.image_types' do
      it 'returns the correct image types' do
        expect(DriverLicense::IMAGE_TYPES).to eq(%w[front back])
      end
    end

    describe '.valid_image_type?' do
      it 'returns true for valid image types' do
        driver_license = DriverLicense.new
        expect(driver_license.valid_image_type?('front')).to be true
        expect(driver_license.valid_image_type?('back')).to be true
      end

      it 'returns false for invalid image types' do
        driver_license = DriverLicense.new
        expect(driver_license.valid_image_type?('invalid')).to be false
        expect(driver_license.valid_image_type?('side')).to be false
        expect(driver_license.valid_image_type?(nil)).to be false
        expect(driver_license.valid_image_type?('')).to be false
      end
    end
  end

  describe 'real image attachments' do
    let(:driver_license) { create(:driver_license) }

    describe 'front_image' do
      it 'is valid with a proper image file' do
        driver_license.front_image.attach(
          io: StringIO.new(File.binread(Rails.root.join('spec', 'fixtures', 'files', 'test_logo.png'))),
          filename: 'front_license.png',
          content_type: 'image/png'
        )
        expect(driver_license).to be_valid
        expect(driver_license.front_image).to be_attached
      end
    end

    describe 'back_image' do
      it 'is valid with a proper image file' do
        driver_license.back_image.attach(
          io: StringIO.new(File.binread(Rails.root.join('spec', 'fixtures', 'files', 'test_logo.png'))),
          filename: 'back_license.png',
          content_type: 'image/png'
        )
        expect(driver_license).to be_valid
        expect(driver_license.back_image).to be_attached
      end
    end

    describe 'both images' do
      it 'is valid with both proper image files' do
        driver_license.front_image.attach(
          io: StringIO.new(File.binread(Rails.root.join('spec', 'fixtures', 'files', 'test_logo.png'))),
          filename: 'front_license.png',
          content_type: 'image/png'
        )
        driver_license.back_image.attach(
          io: StringIO.new(File.binread(Rails.root.join('spec', 'fixtures', 'files', 'test_logo.png'))),
          filename: 'back_license.png',
          content_type: 'image/png'
        )
        expect(driver_license).to be_valid
        expect(driver_license.front_image).to be_attached
        expect(driver_license.back_image).to be_attached
      end
    end
  end

  describe 'image management' do
    let(:driver_license) { create(:driver_license) }
    let(:image_file) do
      Rack::Test::UploadedFile.new(
        Rails.root.join('spec', 'fixtures', 'files', 'test_logo.png'),
        'image/png'
      )
    end

    describe '#attach_image' do
      it 'attaches front image successfully' do
        expect {
          driver_license.attach_image('front', image_file)
        }.to change { driver_license.front_image.attached? }.from(false).to(true)
      end

      it 'attaches back image successfully' do
        expect {
          driver_license.attach_image('back', image_file)
        }.to change { driver_license.back_image.attached? }.from(false).to(true)
      end

      it 'raises error for invalid image type' do
        expect {
          driver_license.attach_image('invalid', image_file)
        }.to raise_error(Errors::InvalidInput, 'Invalid image type')
      end

      it 'raises error when image file is missing' do
        expect {
          driver_license.attach_image('front', nil)
        }.to raise_error(Errors::InvalidInput, 'Image file is required')
      end
    end

    describe '#remove_image' do
      before do
        driver_license.front_image.attach(image_file)
        driver_license.back_image.attach(image_file)
      end

      it 'removes front image successfully' do
        expect {
          driver_license.remove_image('front')
        }.to change { driver_license.front_image.attached? }.from(true).to(false)
      end

      it 'removes back image successfully' do
        expect {
          driver_license.remove_image('back')
        }.to change { driver_license.back_image.attached? }.from(true).to(false)
      end

      it 'raises error for invalid image type' do
        expect {
          driver_license.remove_image('invalid')
        }.to raise_error(Errors::InvalidInput, 'Invalid image type')
      end

      it 'raises error when trying to remove non-attached front image' do
        driver_license.front_image.purge
        expect {
          driver_license.remove_image('front')
        }.to raise_error(Errors::InvalidInput, 'No front image attached')
      end

      it 'raises error when trying to remove non-attached back image' do
        driver_license.back_image.purge
        expect {
          driver_license.remove_image('back')
        }.to raise_error(Errors::InvalidInput, 'No back image attached')
      end
    end
  end

  describe 'image URL methods' do
    let(:driver_license) { create(:driver_license) }

    describe '#front_image_url' do
      context 'when front image is attached' do
        before do
          driver_license.front_image.attach(
            io: StringIO.new(File.binread(Rails.root.join('spec', 'fixtures', 'files', 'test_logo.png'))),
            filename: 'front_license.png',
            content_type: 'image/png'
          )
        end

        it 'returns the image URL' do
          url = driver_license.front_image_url
          expect(url).to be_present
          expect(url).to include('front_license.png')
        end
      end

      context 'when front image is not attached' do
        it 'returns nil' do
          expect(driver_license.front_image_url).to be_nil
        end
      end
    end

    describe '#back_image_url' do
      context 'when back image is attached' do
        before do
          driver_license.back_image.attach(
            io: StringIO.new(File.binread(Rails.root.join('spec', 'fixtures', 'files', 'test_logo.png'))),
            filename: 'back_license.png',
            content_type: 'image/png'
          )
        end

        it 'returns the image URL' do
          url = driver_license.back_image_url
          expect(url).to be_present
          expect(url).to include('back_license.png')
        end
      end

      context 'when back image is not attached' do
        it 'returns nil' do
          expect(driver_license.back_image_url).to be_nil
        end
      end
    end
  end
end
