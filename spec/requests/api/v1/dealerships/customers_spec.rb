require "rails_helper"
require "swagger_helper"

RSpec.describe "Api::V1::Customers", type: :request do
  include_context "dealership_api_shared_context"

  path "/api/v1/dealerships/{dealership_uuid}/customers" do
    get "Get customers list with optional search" do
      tags "Customers"
      produces "application/json"
      security [ Bearer: [] ]

      parameter name: "Authorization", in: :header, type: :string, required: true, description: "Bearer token"
      parameter name: "Device-ID", in: :header, type: :string, required: true, description: "Device identifier"
      parameter name: :dealership_uuid, in: :path, type: :string, required: true, description: "Dealership UUID"
      parameter name: :page, in: :query, type: :integer, required: false, description: "Page number (default: 1)"
      parameter name: :per_page, in: :query, type: :integer, required: false, description: "Items per page (default: 20, max: 100)"

      response "200", "Customers retrieved successfully" do
        let!(:customer1) { create(:customer, dealership: dealership, first_name: "<PERSON>", last_name: "<PERSON>") }
        let!(:customer2) { create(:customer, dealership: dealership, first_name: "<PERSON>", last_name: "<PERSON>") }

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data.dig("status", "message")).to eq("Customers retrieved successfully")
          customers = data.dig("data", "customers")
          expect(customers).to be_an(Array)
          expect(customers.size).to eq(2)
          expect(customers.map { |c| c["first_name"] }).to eq([ "Alice", "Bob" ])
          expect(response.headers['X-Current-Page']).to be_present
          expect(response.headers['X-Per-Page']).to be_present
          expect(response.headers['X-Total-Count']).to be_present
          expect(response.headers['X-Total-Pages']).to be_present
          expect(response.headers['X-Next-Page']).to be_nil
          expect(response.headers['X-Prev-Page']).to be_nil
        end
      end

      response "401", "Unauthorized" do
        let(:Authorization) { "Bearer invalid_token" }

        run_test! do |response|
          expect(response.status).to eq(401)
        end
      end

      response "404", "Dealership not found" do
        let(:dealership_uuid) { "invalid-uuid" }

        run_test! do |response|
          expect(response.status).to eq(404)
        end
      end
    end

    post "Create customer" do
      tags "Customers"
      consumes "application/json"
      produces "application/json"
      security [ Bearer: [] ]

      parameter name: "Authorization", in: :header, type: :string, required: true, description: "Bearer token"
      parameter name: "Device-ID", in: :header, type: :string, required: true, description: "Device identifier"
      parameter name: :dealership_uuid, in: :path, type: :string, required: true, description: "Dealership UUID"

      parameter name: :customer_params, in: :body, schema: {
        type: :object,
        properties: {
          first_name: { type: :string, description: "Customer first name" },
          last_name: { type: :string, description: "Customer last name" },
          email: { type: :string, description: "Customer email" },
          phone_number: { type: :string, description: "Customer phone number" },
          age: { type: :integer, description: "Customer age" },
          gender: { type: :string, enum: [ "unspecified", "male", "female", "other" ], description: "Customer gender" },
          company_name: { type: :string, description: "Company name" },
          external_id: { type: :string, description: "External ID" },
          postcode: { type: :string, description: "Postcode" },
          suburb: { type: :string, description: "Suburb" },
          address_line1: { type: :string, description: "Address line 1" },
          address_line2: { type: :string, description: "Address line 2" },
          city: { type: :string, description: "City" },
          state: { type: :string, description: "State" },
          country: { type: :string, description: "Country" }
        },
        required: [ "first_name", "last_name", "email", "phone_number" ]
      }

      response "200", "Customer created successfully" do
        let(:customer_params) do
          {
            first_name: "John",
            last_name: "Doe",
            email: "<EMAIL>",
            phone_number: "+61400000000",
            age: 30,
            gender: "male"
          }
        end

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data.dig("status", "message")).to eq("Customer created successfully")
          expect(data.dig("data", "customer")).to be_present
        end
      end

      response "422", "Validation errors" do
        let(:customer_params) do
          {
            first_name: "",
            last_name: "",
            email: "invalid-email"
          }
        end

        run_test! do |response|
          expect(response.status).to eq(422)
        end
      end

      response "422", "Duplicate email error" do
        let!(:existing_customer) { create(:customer, dealership: dealership, email: "<EMAIL>") }
        let(:customer_params) do
          {
            first_name: "Jane",
            last_name: "Doe",
            email: "<EMAIL>",
            phone_number: "+61400000001"
          }
        end

        run_test! do |response|
          expect(response.status).to eq(422)
          data = JSON.parse(response.body)
          expect(data.dig("status", "message")).to include("Customer with this email already exists")
        end
      end

      response "401", "Unauthorized" do
        let(:Authorization) { "Bearer invalid_token" }
        let(:customer_params) do
          {
            first_name: "John",
            last_name: "Doe",
            email: "<EMAIL>",
            phone_number: "+61400000000"
          }
        end

        run_test! do |response|
          expect(response.status).to eq(401)
        end
      end

      response "404", "Dealership not found" do
        let(:dealership_uuid) { "invalid-uuid" }
        let(:customer_params) do
          {
            first_name: "John",
            last_name: "Doe",
            email: "<EMAIL>",
            phone_number: "+61400000000"
          }
        end

        run_test! do |response|
          expect(response.status).to eq(404)
        end
      end
    end
  end

  path "/api/v1/dealerships/{dealership_uuid}/customers/search" do
    get "Get customers list with search for autocomplete feature" do
      tags "Customers"
      produces "application/json"
      security [ Bearer: [] ]

      parameter name: "Authorization", in: :header, type: :string, required: true, description: "Bearer token"
      parameter name: "Device-ID", in: :header, type: :string, required: true, description: "Device identifier"
      parameter name: :dealership_uuid, in: :path, type: :string, required: true, description: "Dealership UUID"
      parameter name: :query, in: :query, type: :string, required: false, description: "Search term (3-20 characters) for name, email, or phone"

      response "200", "Customer search completed successfully" do
        let(:query) { "Alice" }
        let!(:customer1) { create(:customer, dealership: dealership, first_name: "Alice", last_name: "Johnson") }
        let!(:customer2) { create(:customer, dealership: dealership, first_name: "Bob", last_name: "Smith") }

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data.dig("status", "message")).to eq("Customer search completed successfully")
          expect(data.dig("data", "customers")).to be_an(Array)
          customer = data.dig("data", "customers").first
          expect(customer.keys).to contain_exactly("uuid", "full_name", "email", "phone_number", "created_at")
        end
      end

      response "200", "Empty search results for short query" do
        let(:query) { "Al" }

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data.dig("status", "message")).to eq("Customer search completed successfully")
          expect(data.dig("data", "customers")).to be_empty
        end
      end

      response "401", "Unauthorized" do
        let(:Authorization) { "Bearer invalid_token" }

        run_test! do |response|
          expect(response.status).to eq(401)
        end
      end

      response "404", "Dealership not found" do
        let(:dealership_uuid) { "invalid-uuid" }
        let(:query) { "Alice" }

        run_test! do |response|
          expect(response.status).to eq(404)
        end
      end
    end
  end

  path "/api/v1/dealerships/{dealership_uuid}/customers/{customer_uuid}" do
    get "Get customer details" do
      tags "Customers"
      produces "application/json"
      security [ Bearer: [] ]

      parameter name: "Authorization", in: :header, type: :string, required: true, description: "Bearer token"
      parameter name: "Device-ID", in: :header, type: :string, required: true, description: "Device identifier"
      parameter name: :dealership_uuid, in: :path, type: :string, required: true, description: "Dealership UUID"
      parameter name: :customer_uuid, in: :path, type: :string, required: true, description: "Customer UUID"

      response "200", "Customer retrieved successfully" do
        let!(:customer) { create(:customer, dealership: dealership) }
        let(:customer_uuid) { customer.uuid }

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data.dig("status", "message")).to eq("Customer retrieved successfully")
          expect(data.dig("data", "customer")).to be_present
        end
      end

      response "404", "Customer not found" do
        let(:customer_uuid) { "invalid-uuid" }

        run_test! do |response|
          expect(response.status).to eq(404)
        end
      end

      response "401", "Unauthorized" do
        let(:Authorization) { "Bearer invalid_token" }
        let!(:customer) { create(:customer, dealership: dealership) }
        let(:customer_uuid) { customer.uuid }

        run_test! do |response|
          expect(response.status).to eq(401)
        end
      end
    end

    put "Update customer" do
      tags "Customers"
      consumes "application/json"
      produces "application/json"
      security [ Bearer: [] ]

      parameter name: "Authorization", in: :header, type: :string, required: true, description: "Bearer token"
      parameter name: "Device-ID", in: :header, type: :string, required: true, description: "Device identifier"
      parameter name: :dealership_uuid, in: :path, type: :string, required: true, description: "Dealership UUID"
      parameter name: :customer_uuid, in: :path, type: :string, required: true, description: "Customer UUID"

      parameter name: :customer_params, in: :body, schema: {
        type: :object,
        properties: {
          first_name: { type: :string, description: "Customer first name" },
          last_name: { type: :string, description: "Customer last name" },
          email: { type: :string, description: "Customer email" },
          phone_number: { type: :string, description: "Customer phone number" },
          age: { type: :integer, description: "Customer age" },
          gender: { type: :string, enum: [ "unspecified", "male", "female", "other" ], description: "Customer gender" },
          company_name: { type: :string, description: "Company name" },
          external_id: { type: :string, description: "External ID" },
          postcode: { type: :string, description: "Postcode" },
          suburb: { type: :string, description: "Suburb" },
          address_line1: { type: :string, description: "Address line 1" },
          address_line2: { type: :string, description: "Address line 2" },
          city: { type: :string, description: "City" },
          state: { type: :string, description: "State" },
          country: { type: :string, description: "Country" }
        }
      }

      response "200", "Customer updated successfully" do
        let!(:customer) { create(:customer, dealership: dealership) }
        let(:customer_uuid) { customer.uuid }
        let(:customer_params) { { first_name: "Updated Name" } }

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data.dig("status", "message")).to eq("Customer updated successfully")
          expect(data.dig("data", "customer", "first_name")).to eq("Updated Name")
        end
      end

      response "404", "Customer not found" do
        let(:customer_uuid) { "invalid-uuid" }
        let(:customer_params) { { first_name: "Jane" } }

        run_test! do |response|
          expect(response.status).to eq(404)
        end
      end

      response "401", "Unauthorized" do
        let(:Authorization) { "Bearer invalid_token" }
        let!(:customer) { create(:customer, dealership: dealership) }
        let(:customer_uuid) { customer.uuid }
        let(:customer_params) { { first_name: "Jane" } }

        run_test! do |response|
          expect(response.status).to eq(401)
        end
      end
    end
  end

  path "/api/v1/dealerships/{dealership_uuid}/customers/{customer_uuid}/driving-license" do
    get "Get customer's driving license" do
      tags "Customers"
      produces "application/json"
      security [ Bearer: [] ]

      parameter name: "Authorization", in: :header, type: :string, required: true, description: "Bearer token"
      parameter name: "Device-ID", in: :header, type: :string, required: true, description: "Device identifier"
      parameter name: :dealership_uuid, in: :path, type: :string, required: true, description: "Dealership UUID"
      parameter name: :customer_uuid, in: :path, type: :string, required: true, description: "Customer UUID"

      response "200", "Driving license retrieved successfully" do
        let!(:customer) { create(:customer, dealership: dealership) }
        let(:customer_uuid) { customer.uuid }
        let!(:driver_license) { create(:driver_license, holder: customer) }

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data.dig("status", "message")).to eq("Driving license retrieved successfully")
          expect(data.dig("data", "driving_license", "uuid")).to eq(driver_license.uuid)
        end
      end

      response "404", "Driving license not found" do
        let!(:customer) { create(:customer, dealership: dealership) }
        let(:customer_uuid) { customer.uuid }
        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data.dig("status", "message")).to eq("Driving license not found")
        end
      end

      response "401", "Unauthorized" do
        let!(:customer) { create(:customer, dealership: dealership) }
        let(:customer_uuid) { customer.uuid }
        let(:Authorization) { "Bearer invalid_token" }

        run_test! do |response|
          expect(response.status).to eq(401)
        end
      end
    end

    post "Create or update driving license" do
      tags "Customers"
      consumes "application/json"
      produces "application/json"
      security [ Bearer: [] ]

      parameter name: "Authorization", in: :header, type: :string, required: true, description: "Bearer token"
      parameter name: "Device-ID", in: :header, type: :string, required: true, description: "Device identifier"
      parameter name: :dealership_uuid, in: :path, type: :string, required: true, description: "Dealership UUID"
      parameter name: :customer_uuid, in: :path, type: :string, required: true, description: "Customer UUID"

      parameter name: :license_params, in: :body, schema: {
        type: :object,
        properties: {
          licence_number: { type: :string, example: "DL123456789" },
          expiry_date: { type: :string, format: :date, example: "2025-12-31" },
          issue_date: { type: :string, format: :date, example: "2020-01-01" },
          category: { type: :string, example: "C" },
          issuing_country: { type: :string, example: "au" },
          issuing_state: { type: :string, example: "NSW" },
          full_name: { type: :string, example: "John Doe" },
          date_of_birth: { type: :string, format: :date, example: "1990-01-01" }
        },
        required: [ "licence_number", "expiry_date" ]
      }

      response "200", "Driving license created/updated successfully" do
        let!(:customer) { create(:customer, dealership: dealership) }
        let(:customer_uuid) { customer.uuid }
        let(:license_params) do
          {
            licence_number: "DL123456789",
            expiry_date: "2025-12-31",
            issue_date: "2020-01-01",
            category: "C",
            issuing_country: "au",
            issuing_state: "NSW",
            full_name: "John Doe",
            date_of_birth: "1990-01-01"
          }
        end

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data.dig("status", "message")).to include("successfully")
          expect(data.dig("data", "driving_license", "licence_number")).to eq("DL123456789")
        end
      end

      response "422", "Validation failed" do
        let!(:customer) { create(:customer, dealership: dealership) }
        let(:customer_uuid) { customer.uuid }
        let(:license_params) { { licence_number: "" } }

        run_test! do |response|
          JSON.parse(response.body)
          expect(response.status).to eq(422)
        end
      end

      response "401", "Unauthorized" do
        let!(:customer) { create(:customer, dealership: dealership) }
        let(:customer_uuid) { customer.uuid }
        let(:Authorization) { "Bearer invalid_token" }
        let(:license_params) { { licence_number: "DL123456789" } }

        run_test! do |response|
          expect(response.status).to eq(401)
        end
      end
    end

    delete "Delete driving license" do
      tags "Customers"
      produces "application/json"
      security [ Bearer: [] ]

      parameter name: "Authorization", in: :header, type: :string, required: true, description: "Bearer token"
      parameter name: "Device-ID", in: :header, type: :string, required: true, description: "Device identifier"
      parameter name: :dealership_uuid, in: :path, type: :string, required: true, description: "Dealership UUID"
      parameter name: :customer_uuid, in: :path, type: :string, required: true, description: "Customer UUID"

      response "200", "Driving license deleted successfully" do
        let!(:customer) { create(:customer, dealership: dealership) }
        let(:customer_uuid) { customer.uuid }
        let!(:driver_license) { create(:driver_license, holder: customer) }

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data.dig("status", "message")).to eq("Driving license deleted successfully")
        end
      end

      response "404", "Driving license not found" do
        let!(:customer) { create(:customer, dealership: dealership) }
        let(:customer_uuid) { customer.uuid }
        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data.dig("status", "message")).to eq("Driving license not found")
        end
      end

      response "401", "Unauthorized" do
        let!(:customer) { create(:customer, dealership: dealership) }
        let(:customer_uuid) { customer.uuid }
        let(:Authorization) { "Bearer invalid_token" }

        run_test! do |response|
          expect(response.status).to eq(401)
        end
      end
    end
  end

  path "/api/v1/dealerships/{dealership_uuid}/customers/{customer_uuid}/driving-license-image" do
    post "Upload driving license image" do
      tags "Customers"
      consumes "multipart/form-data"
      produces "application/json"
      security [ Bearer: [] ]

      parameter name: "Authorization", in: :header, type: :string, required: true, description: "Bearer token"
      parameter name: "Device-ID", in: :header, type: :string, required: true, description: "Device identifier"
      parameter name: :dealership_uuid, in: :path, type: :string, required: true, description: "Dealership UUID"
      parameter name: :customer_uuid, in: :path, type: :string, required: true, description: "Customer UUID"
      parameter name: :image_type, in: :formData, type: :string, enum: [ "front", "back" ], required: true, description: "Type of image"
      parameter name: :image, in: :formData, type: :file, required: true, description: "Image file"

      response "200", "Image uploaded successfully" do
        let!(:customer) { create(:customer, dealership: dealership) }
        let(:customer_uuid) { customer.uuid }
        let!(:driver_license) { create(:driver_license, holder: customer) }
        let(:image_type) { "front" }
        let(:image) { fixture_file_upload("spec/fixtures/files/front_dl.png", "image/png") }

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data.dig("status", "message")).to include("image uploaded successfully")
          expect(data.dig("data", "driving_license")).to be_present
        end
      end

      response "404", "Driving license not found" do
        let!(:customer) { create(:customer, dealership: dealership) }
        let(:customer_uuid) { customer.uuid }
        let(:image_type) { "front" }
        let(:image) { fixture_file_upload("spec/fixtures/files/front_dl.png", "image/png") }

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data.dig("status", "message")).to eq("Driving license not found")
        end
      end

      response "422", "Invalid image type or missing file" do
        let!(:customer) { create(:customer, dealership: dealership) }
        let(:customer_uuid) { customer.uuid }
        let!(:driver_license) { create(:driver_license, holder: customer) }
        let(:image_type) { "invalid" }
        let(:image) { fixture_file_upload("spec/fixtures/files/front_dl.png", "image/png") }

        run_test! do |response|
          expect(response.status).to eq(422)
        end
      end

      response "401", "Unauthorized" do
        let!(:customer) { create(:customer, dealership: dealership) }
        let(:customer_uuid) { customer.uuid }
        let!(:driver_license) { create(:driver_license, holder: customer) }
        let(:Authorization) { "Bearer invalid_token" }
        let(:image_type) { "front" }
        let(:image) { fixture_file_upload("spec/fixtures/files/front_dl.png", "image/png") }

        run_test! do |response|
          expect(response.status).to eq(401)
        end
      end
    end

    delete "Delete driving license image" do
      tags "Customers"
      produces "application/json"
      security [ Bearer: [] ]

      parameter name: "Authorization", in: :header, type: :string, required: true, description: "Bearer token"
      parameter name: "Device-ID", in: :header, type: :string, required: true, description: "Device identifier"
      parameter name: :dealership_uuid, in: :path, type: :string, required: true, description: "Dealership UUID"
      parameter name: :customer_uuid, in: :path, type: :string, required: true, description: "Customer UUID"
      parameter name: :image_type, in: :query, type: :string, enum: [ "front", "back" ], required: true, description: "Type of image to delete"

      response "200", "Image deleted successfully" do
        let!(:customer) { create(:customer, dealership: dealership) }
        let(:customer_uuid) { customer.uuid }
        let!(:driver_license) do
          license = create(:driver_license, holder: customer)
          license.front_image.attach(
            io: File.open(Rails.root.join("spec", "fixtures", "files", "test_logo.png")),
            filename: "front.png",
            content_type: "image/png"
          )
          license
        end
        let(:image_type) { "front" }

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data.dig("status", "message")).to include("image deleted successfully")
          expect(data.dig("data", "driving_license")).to be_present
        end
      end

      response "404", "Driving license not found" do
        let!(:customer) { create(:customer, dealership: dealership) }
        let(:customer_uuid) { customer.uuid }
        let(:image_type) { "front" }

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data.dig("status", "message")).to eq("Driving license not found")
        end
      end

      response "422", "Image not attached or invalid type" do
        let!(:customer) { create(:customer, dealership: dealership) }
        let(:customer_uuid) { customer.uuid }
        let!(:driver_license) { create(:driver_license, holder: customer) }
        let(:image_type) { "front" }

        run_test! do |response|
          expect(response.status).to eq(422)
        end
      end

      response "401", "Unauthorized" do
        let!(:customer) { create(:customer, dealership: dealership) }
        let(:customer_uuid) { customer.uuid }
        let!(:driver_license) { create(:driver_license, holder: customer) }
        let(:Authorization) { "Bearer invalid_token" }
        let(:image_type) { "front" }

        run_test! do |response|
          expect(response.status).to eq(401)
        end
      end
    end
  end

  describe "GET /api/v1/dealerships/:dealership_uuid/customers" do
    subject { get "/api/v1/dealerships/#{dealership.uuid}/customers", params: params, headers: headers }

    let!(:customers) { create_list(:customer, 25, dealership: dealership) }

    context "with valid dealership uuid" do
      let(:params) { {} }

      it "returns all customers ordered by first name" do
        subject
        expect(response).to have_http_status(:ok)
        json = response.parsed_body

        expect(json.dig("status", "message")).to eq("Customers retrieved successfully")
        customers = json.dig("data", "customers")
        expect(customers.size).to eq(20)  # Default per_page
      end

      it "includes enhanced Pagy pagination metadata" do
        subject

        expect(response.headers['X-Current-Page']).to eq('1')
        expect(response.headers['X-Per-Page']).to eq('20')
        expect(response.headers['X-Total-Count']).to eq('25')
        expect(response.headers['X-Total-Pages']).to eq('2')
        expect(response.headers['X-Next-Page']).to eq('2')
        expect(response.headers['X-Prev-Page']).to be_nil
      end
    end

    context "with pagination parameters" do
      let(:params) { { page: 3, per_page: 10 } }

      it "returns paginated results" do
        subject
        expect(response).to have_http_status(:ok)
        json = response.parsed_body

        customers = json.dig("data", "customers")
        expect(customers.size).to eq(5)

        expect(response.headers['X-Current-Page']).to eq('3')
        expect(response.headers['X-Per-Page']).to eq('10')
        expect(response.headers['X-Total-Count']).to eq('25')
        expect(response.headers['X-Total-Pages']).to eq('3')
        expect(response.headers['X-Next-Page']).to be_nil
        expect(response.headers['X-Prev-Page']).to eq('2')
      end
    end

    context "with per_page exceeding maximum" do
      let(:params) { { per_page: 150 } }

      it "limits per_page to maximum allowed" do
        subject
        expect(response).to have_http_status(:ok)
        expect(response.headers['X-Per-Page']).to eq('100')
      end
    end

    context "with invalid dealership uuid" do
      subject { get "/api/v1/dealerships/invalid-uuid/customers", headers: headers }

      it "returns not found" do
        subject
        expect(response).to have_http_status(:not_found)
      end
    end

    context "without authentication" do
      let(:headers) { {} }
      let(:params) { {} }

      it "returns unauthorized" do
        subject
        expect(response).to have_http_status(:unauthorized)
      end
    end
  end

  describe "GET /api/v1/dealerships/:dealership_uuid/customers/search" do
    subject { get "/api/v1/dealerships/#{dealership.uuid}/customers/search", params: params, headers: headers }

    let!(:customer1) { create(:customer, dealership: dealership, first_name: "Alice", last_name: "Johnson", email: "<EMAIL>") }
    let!(:customer2) { create(:customer, dealership: dealership, first_name: "Bob", last_name: "Smith", email: "<EMAIL>") }
    let!(:customer3) { create(:customer, dealership: dealership, first_name: "Charlie", last_name: "Brown", phone_number: "+61400123456") }

    context "with search query" do
      let(:params) { { query: "alice" } }

      it "returns matching customers with limited fields" do
        subject
        expect(response).to have_http_status(:ok)
        json = response.parsed_body

        expect(json.dig("status", "message")).to eq("Customer search completed successfully")
        customers = json.dig("data", "customers")
        expect(customers.size).to eq(1)

        customer = customers.first
        expect(customer.keys).to contain_exactly("uuid", "full_name", "email", "phone_number", "created_at")
        expect(customer["full_name"]).to include("Alice")
      end
    end

    context "with search query containing whitespace" do
      let(:params) { { query: "  alice  " } }

      it "strips whitespace and returns results" do
        subject
        expect(response).to have_http_status(:ok)
        json = response.parsed_body

        customers = json.dig("data", "customers")
        expect(customers.size).to eq(1)
        expect(customers.first["full_name"]).to include("Alice")
      end
    end

    context "with case insensitive search" do
      let(:params) { { query: "ALICE" } }

      it "finds results regardless of case" do
        subject
        expect(response).to have_http_status(:ok)
        json = response.parsed_body

        customers = json.dig("data", "customers")
        expect(customers.size).to eq(1)
        expect(customers.first["full_name"]).to include("Alice")
      end
    end

    context "with short search query (less than 3 characters)" do
      let(:params) { { query: "Al" } }

      it "returns empty results" do
        subject
        expect(response).to have_http_status(:ok)
        json = response.parsed_body

        expect(json.dig("status", "message")).to eq("Customer search completed successfully")
        customers = json.dig("data", "customers")
        expect(customers).to be_empty
      end
    end

    context "with long search query (more than 20 characters)" do
      let(:params) { { query: "a" * 25 } }

      it "returns empty results" do
        subject
        expect(response).to have_http_status(:ok)
        json = response.parsed_body

        expect(json.dig("status", "message")).to eq("Customer search completed successfully")
        customers = json.dig("data", "customers")
        expect(customers).to be_empty
      end
    end

    context "with invalid dealership uuid" do
      subject { get "/api/v1/dealerships/invalid-uuid/customers/search", params: params, headers: headers }
      let(:params) { { query: "Alice" } }

      it "returns not found" do
        subject
        expect(response).to have_http_status(:not_found)
      end
    end

    context "without dealership uuid" do
      subject { get "/api/v1/dealerships/customers/search", params: params, headers: headers }
      let(:params) { { query: "Alice" } }

      it "returns not found" do
        subject
        expect(response).to have_http_status(:not_found)
      end
    end

    context "without authentication" do
      let(:headers) { {} }
      let(:params) { { dealership_uuid: dealership.uuid } }

      it "returns unauthorized" do
        subject
        expect(response).to have_http_status(:unauthorized)
      end
    end

    context "with more than 20 records" do
      let!(:customers) { create_list(:customer, 30, dealership: dealership, first_name: 'Alice') }
      let(:params) { { query: "alice" } }

      it "limits results to 20" do
        subject
        expect(response).to have_http_status(:ok)
        json = response.parsed_body

        customers = json.dig("data", "customers")
        expect(customers.size).to eq(20)
      end
    end
  end

  describe "POST /api/v1/dealerships/:dealership_uuid/customers" do
    subject { post "/api/v1/dealerships/#{dealership_uuid}/customers", params: params, headers: headers, as: :json }

    let(:valid_params) do
      {
        first_name: "John",
        last_name: "Doe",
        email: "<EMAIL>",
        phone_number: "+61400000000",
        age: 30,
        gender: "male"
      }
    end

    context "with valid parameters" do
      let(:params) { valid_params }

      it "creates a new customer" do
        expect { subject }.to change(Customer, :count).by(1)

        expect(response).to have_http_status(:ok)
        json = response.parsed_body
        expect(json.dig("status", "message")).to eq("Customer created successfully")
        expect(json.dig("data", "customer", "first_name")).to eq("John")
      end
    end

    context "with invalid parameters" do
      let(:params) { valid_params.merge(email: "invalid-email") }

      it "returns validation errors" do
        subject
        expect(response).to have_http_status(:unprocessable_entity)
      end
    end

    context "with duplicate email" do
      let!(:existing_customer) { create(:customer, dealership: dealership, email: "<EMAIL>") }
      let(:params) { valid_params.merge(email: "<EMAIL>") }

      it "prevents duplicate email creation" do
        subject
        expect(response).to have_http_status(:unprocessable_entity)
        json = response.parsed_body
        expect(json.dig("status", "message")).to include("Customer with this email already exists")
      end
    end

    context "with invalid dealership uuid" do
      subject { post "/api/v1/dealerships/invalid-uuid/customers", params: params.to_json, headers: headers }
      let(:params) { valid_params }

      it "returns not found" do
        subject
        expect(response).to have_http_status(:not_found)
      end
    end

    context "without authentication" do
      let(:headers) { {} }
      let(:params) { valid_params }

      it "returns unauthorized" do
        subject
        expect(response).to have_http_status(:unauthorized)
      end
    end
  end

  describe "GET /api/v1/dealerships/:dealership_uuid/customers/:customer_uuid" do
    subject { get "/api/v1/dealerships/#{dealership.uuid}/customers/#{customer_uuid}", headers: headers }

    let!(:customer) { create(:customer, dealership: dealership) }

    context "with valid customer ID" do
      let(:customer_uuid) { customer.uuid }

      it "returns the customer details" do
        subject
        expect(response).to have_http_status(:ok)
        json = response.parsed_body

        expect(json.dig("status", "message")).to eq("Customer retrieved successfully")
        expect(json.dig("data", "customer", "uuid")).to eq(customer.uuid)
      end
    end

    context "with invalid customer ID" do
      let(:customer_uuid) { "invalid-uuid" }

      it "returns not found" do
        subject
        expect(response).to have_http_status(:not_found)
      end
    end

    context "with customer from different dealership" do
      let(:other_dealership) { create(:dealership) }
      let(:other_customer) { create(:customer, dealership: other_dealership) }
      let(:customer_uuid) { other_customer.uuid }

      it "returns not found" do
        subject
        expect(response).to have_http_status(:not_found)
      end
    end

    context "without authentication" do
      let(:headers) { {} }
      let(:customer_uuid) { customer.uuid }

      it "returns unauthorized" do
        subject
        expect(response).to have_http_status(:unauthorized)
      end
    end
  end

  describe "PUT /api/v1/dealerships/:dealership_uuid/customers/:customer_uuid" do
    subject { put "/api/v1/dealerships/#{dealership.uuid}/customers/#{customer_uuid}", params: params, headers: headers, as: :json }

    let!(:customer) { create(:customer, dealership: dealership, first_name: "Original") }

    context "with valid parameters" do
      let(:customer_uuid) { customer.uuid }
      let(:params) { { first_name: "Updated" } }

      it "updates the customer" do
        subject
        expect(response).to have_http_status(:ok)
        json = response.parsed_body

        expect(json.dig("status", "message")).to eq("Customer updated successfully")
        expect(json.dig("data", "customer", "first_name")).to eq("Updated")
      end
    end

    context "with invalid customer ID" do
      let(:customer_uuid) { "invalid-uuid" }
      let(:params) { { first_name: "Jane" } }

      it "returns not found" do
        subject
        expect(response).to have_http_status(:not_found)
      end
    end

    context "with customer from different dealership" do
      let(:other_dealership) { create(:dealership) }
      let(:other_customer) { create(:customer, dealership: other_dealership) }
      let(:customer_uuid) { other_customer.uuid }
      let(:params) { { first_name: "Jane" } }

      it "returns not found" do
        subject
        expect(response).to have_http_status(:not_found)
      end
    end

    context "without authentication" do
      let(:headers) { {} }
      let(:customer_uuid) { customer.uuid }
      let(:params) { { first_name: "Jane" } }

      it "returns unauthorized" do
        subject
        expect(response).to have_http_status(:unauthorized)
      end
    end
  end
end
