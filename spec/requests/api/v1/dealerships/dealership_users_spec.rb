require "rails_helper"
require "swagger_helper"

RSpec.describe "Api::V1::Dealerships::DealershipUsers", type: :request do
  include_context "dealership_api_shared_context"

  path "/api/v1/dealerships/{dealership_uuid}/users" do
    get "Get dealership users" do
      tags "Dealership Users"
      produces "application/json"
      security [ Bearer: [] ]

      parameter name: "Authorization", in: :header, type: :string, required: true, description: "Bearer token"
      parameter name: "Device-ID", in: :header, type: :string, required: true, description: "Device identifier"
      parameter name: :dealership_uuid, in: :path, type: :string, required: true, description: "Dealership UUID"
      parameter name: :role_type, in: :query, type: :string, required: false, description: "Filter by user role", enum: [ "dealership_admin", "sales_person", "staff" ]
      parameter name: :page, in: :query, type: :integer, required: false, description: "Page number"
      parameter name: :per_page, in: :query, type: :integer, required: false, description: "Items per page"

      response "200", "Dealership users retrieved successfully" do
        let(:dealership_uuid) { dealership.uuid }
        let!(:user1) { create(:user, first_name: "<PERSON>", last_name: "<PERSON>") }
        let!(:user2) { create(:user, first_name: "Bob", last_name: "Smith") }
        let!(:user3) { create(:user, first_name: "Mark", last_name: "Twain", status: :inactive) }

        before do
          create(:user_dealership, user: user1, dealership: dealership, role: :sales_person)
          create(:user_dealership, user: user2, dealership: dealership, role: :staff)
          create(:user_dealership, user: user3, dealership: dealership, role: :staff)
        end

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data.dig("status", "message")).to eq("Dealership users retrieved successfully")
          expect(data.dig("data", "users")).to be_an(Array)
          users = data.dig("data", "users")
          expect(users.size).to eq(3)
          expect(users.map { |u| u["first_name"] }).to eq([ "Alice", "Bob", user.first_name ])
          expect(users.map { |u| u["dealership_role"] }).to eq([ "sales_person", "staff", "dealership_admin" ])
          expect(response.headers["X-Current-Page"]).to be_present
          expect(response.headers["X-Per-Page"]).to be_present
          expect(response.headers["X-Total-Count"]).to be_present
        end
      end

      response "422", "Empty results for invalid role filter" do
        let(:dealership_uuid) { dealership.uuid }
        let(:role_type) { "invalid_role" }

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(response.status).to eq(422)
          expect(data.dig("status", "message")).to include("Invalid role filter")
        end
      end

      response "404", "Dealership not found" do
        let(:dealership_uuid) { "non-existent-uuid" }

        run_test! do |response|
          expect(response.status).to eq(404)
        end
      end

      response "404", "Dealership not found" do
        let(:other_dealership) { create(:dealership) }
        let(:dealership_uuid) { other_dealership.uuid }

        run_test! do |response|
          expect(response.status).to eq(404)
        end
      end

      response "401", "Unauthorized" do
        let(:Authorization) { "Bearer invalid_token" }
        let(:dealership_uuid) { dealership.uuid }

        run_test! do |response|
          expect(response.status).to eq(401)
        end
      end
    end
  end

  describe "GET /api/v1/dealerships/:dealership_uuid/users" do
    subject { get "/api/v1/dealerships/#{dealership_uuid}/users", params: params, headers: headers }

    let(:dealership_uuid) { dealership.uuid }
    let!(:user1) { create(:user, first_name: "Alice", last_name: "Johnson") }
    let!(:user2) { create(:user, first_name: "Bob", last_name: "Smith") }
    let!(:user3) { create(:user, first_name: "Charlie", last_name: "Brown") }
    let!(:user4) { create(:user, first_name: "Michael", last_name: "Jackson", status: :inactive) }

    before do
      create(:user_dealership, user: user1, dealership: dealership, role: :sales_person)
      create(:user_dealership, user: user2, dealership: dealership, role: :staff)
      create(:user_dealership, user: user3, dealership: dealership, role: :dealership_admin)
    end

    context "without filter parameters" do
      let(:params) { {} }

      it "returns all dealership users ordered by first name" do
        subject
        expect(response).to have_http_status(:ok)
        json = response.parsed_body

        expect(json.dig("status", "message")).to eq("Dealership users retrieved successfully")
        users = json.dig("data", "users")
        expect(users.size).to eq(4)

        first_names = users.map { |u| u["first_name"] }
        expect(first_names).to eq(first_names.sort)
      end

      it "sets pagination headers correctly" do
        subject
        expect(response.headers["X-Current-Page"]).to eq("1")
        expect(response.headers["X-Per-Page"]).to eq("20")
        expect(response.headers["X-Total-Count"]).to eq("4")
        expect(response.headers["X-Total-Pages"]).to eq("1")
        expect(response.headers["X-Next-Page"]).to be_nil
        expect(response.headers["X-Prev-Page"]).to be_nil
      end

      it "includes dealership role for each user" do
        subject
        json = response.parsed_body
        users = json.dig("data", "users")

        users.each do |user_data|
          expect(user_data["dealership_role"]).to be_present
          expect([ "dealership_admin", "sales_person", "staff" ]).to include(user_data["dealership_role"])
        end
      end
    end

    context "with role filter" do
      let(:params) { { role_type: "sales_person" } }

      it "filters users by role" do
        subject
        expect(response).to have_http_status(:ok)
        json = response.parsed_body

        users = json.dig("data", "users")
        expect(users.size).to eq(1)
        expect(users.first["first_name"]).to eq("Alice")
        expect(users.first["dealership_role"]).to eq("sales_person")
      end

      it "sets correct pagination headers for filtered results" do
        subject
        expect(response.headers["X-Total-Count"]).to eq("1")
        expect(response.headers["X-Current-Page"]).to eq("1")
        expect(response.headers["X-Total-Pages"]).to eq("1")
      end
    end

    context "with dealership_admin role filter" do
      let(:params) { { role_type: "dealership_admin" } }

      it "filters users by dealership_admin role" do
        subject
        expect(response).to have_http_status(:ok)
        json = response.parsed_body

        users = json.dig("data", "users")
        expect(users.size).to eq(2)

        admin_users = users.select { |u| u["dealership_role"] == "dealership_admin" }
        expect(admin_users.size).to eq(2)
      end
    end

    context "with staff role filter" do
      let(:params) { { role_type: "staff" } }

      it "filters users by staff role" do
        subject
        expect(response).to have_http_status(:ok)
        json = response.parsed_body

        users = json.dig("data", "users")
        expect(users.size).to eq(1)
        expect(users.first["first_name"]).to eq("Bob")
        expect(users.first["dealership_role"]).to eq("staff")
      end
    end

    context "with invalid role filter" do
      let(:params) { { role_type: "invalid_role" } }

      it "returns validation error" do
        subject
        expect(response).to have_http_status(:unprocessable_entity)
        json = response.parsed_body
        expect(json.dig("status", "message")).to include("Invalid role filter")
        expect(json.dig("status", "message")).to include("Valid roles: dealership_admin, sales_person, staff")
      end
    end

    context "with empty role filter" do
      let(:params) { { role_type: "" } }

      it "returns all users when role is empty" do
        subject
        expect(response).to have_http_status(:ok)
        json = response.parsed_body

        users = json.dig("data", "users")
        expect(users.size).to eq(4)
      end
    end

    context "with role filter and pagination" do
      let(:params) { { role_type: "dealership_admin", page: 1, per_page: 1 } }

      it "applies both role filter and pagination" do
        subject
        expect(response).to have_http_status(:ok)
        json = response.parsed_body

        users = json.dig("data", "users")
        expect(users.size).to eq(1)
        expect(users.first["dealership_role"]).to eq("dealership_admin")

        expect(response.headers["X-Total-Count"]).to eq("2")
        expect(response.headers["X-Current-Page"]).to eq("1")
        expect(response.headers["X-Total-Pages"]).to eq("2")
        expect(response.headers["X-Next-Page"]).to eq("2")
      end
    end

    context "with pagination parameters" do
      let(:params) { { page: 2, per_page: 2 } }

      it "returns paginated results" do
        subject
        expect(response).to have_http_status(:ok)
        json = response.parsed_body

        users = json.dig("data", "users")
        expect(users.size).to eq(2)
      end

      it "sets pagination headers with next/prev page numbers" do
        subject
        expect(response.headers["X-Current-Page"]).to eq("2")
        expect(response.headers["X-Per-Page"]).to eq("2")
        expect(response.headers["X-Total-Count"]).to eq("4")
        expect(response.headers["X-Total-Pages"]).to eq("2")
        expect(response.headers["X-Next-Page"]).to be_nil
        expect(response.headers["X-Prev-Page"]).to eq("1")
      end
    end

    context "with invalid dealership UUID" do
      let(:dealership_uuid) { "non-existent-uuid" }
      let(:params) { {} }

      it "returns not found" do
        subject
        expect(response).to have_http_status(:not_found)
      end
    end

    context "without authentication" do
      let(:headers) { {} }
      let(:params) { {} }
    end

    context "without dealership access" do
      let(:other_dealership) { create(:dealership) }
      let(:dealership_uuid) { other_dealership.uuid }
      let(:params) { {} }

      it "returns not found" do
        subject
        expect(response).to have_http_status(:not_found)
      end
    end

    context "with large per_page value" do
      let(:params) { { per_page: 200 } }

      it "limits per_page to maximum allowed" do
        subject
        expect(response).to have_http_status(:ok)
        expect(response.headers['X-Per-Page']).to eq('100')
      end
    end
  end
end
