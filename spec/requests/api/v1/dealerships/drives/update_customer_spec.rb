# frozen_string_literal: true

require "rails_helper"
require "swagger_helper"

RSpec.describe "Api::V1::Dealerships::DrivesController", type: :request do
  include_context "drive_api_shared_context"

  describe "PUT /api/v1/dealerships/:dealership_uuid/drives/:uuid/customer" do
    let(:test_drive) { create(:drive, dealership: dealership, vehicle: vehicle, sales_person: sales_person) }
    let(:url) { "/api/v1/dealerships/#{dealership.uuid}/drives/#{test_drive.uuid}/customer" }

    context "with existing customer" do
      let(:existing_customer) { create(:customer, dealership: dealership) }
      let(:params) { { customer_uuid: existing_customer.uuid } }

      it "updates the drive customer successfully" do
        put url, params: params, headers: headers
        expect(response).to have_http_status(:ok)

        json = response.parsed_body
        expect(json.dig("status", "message")).to eq("Customer updated successfully")
        expect(json.dig("data", "drive", "customer", "uuid")).to eq(existing_customer.uuid)
      end
    end

    context "with new customer info" do
      let(:params) do
        {
          customer_info: {
            first_name: "<PERSON>",
            last_name: "<PERSON>",
            email: "<EMAIL>",
            phone_number: "+61423456789",
            gender: "female"
          }
        }
      end

      it "creates and assigns new customer successfully" do
        put url, params: params, headers: headers
        expect(response).to have_http_status(:ok)

        json = response.parsed_body
        expect(json.dig("status", "message")).to eq("Customer updated successfully")
        expect(json.dig("data", "drive", "customer", "first_name")).to eq("Jane")
        expect(json.dig("data", "drive", "customer", "last_name")).to eq("Smith")
        expect(json.dig("data", "drive", "customer", "email")).to eq("<EMAIL>")
      end
    end

    context "with new customer info and driver license" do
      let(:params) do
        {
          customer_info: {
            first_name: "Robert",
            last_name: "Johnson",
            email: "<EMAIL>",
            phone_number: "+61434567890",
            gender: "male",
            driver_license: {
              licence_number: "**********",
              expiry_date: "2025-12-31",
              issuing_state: "NSW",
              issuing_country: "AU",
              full_name: "Robert Johnson",
              date_of_birth: "1985-05-15"
            }
          }
        }
      end

      it "creates customer with driver license successfully" do
        put url, params: params, headers: headers
        expect(response).to have_http_status(:ok)

        json = response.parsed_body
        expect(json.dig("status", "message")).to eq("Customer updated successfully")
        expect(json.dig("data", "drive", "customer", "first_name")).to eq("Robert")
        expect(json.dig("data", "drive", "customer", "driver_license")).to be_present
        expect(json.dig("data", "drive", "customer", "driver_license", "licence_number")).to eq("**********")
        expect(json.dig("data", "drive", "customer", "driver_license", "expiry_date")).to eq("2025-12-31")
        expect(json.dig("data", "drive", "customer", "driver_license", "issuing_state")).to eq("NSW")
      end
    end

    context "with new customer info and driver license images" do
      let(:front_image) { fixture_file_upload("spec/fixtures/files/front_dl.png", "image/png") }
      let(:back_image) { fixture_file_upload("spec/fixtures/files/back_dl.png", "image/png") }
      let(:params) do
        {
          customer_info: {
            first_name: "Sarah",
            last_name: "Wilson",
            email: "<EMAIL>",
            phone_number: "+61445678901",
            gender: "female",
            driver_license: {
              licence_number: "**********",
              expiry_date: "2026-06-30",
              issuing_state: "VIC",
              issuing_country: "AU",
              full_name: "Sarah Wilson",
              date_of_birth: "1988-03-20",
              front_image: front_image,
              back_image: back_image
            }
          }
        }
      end

      it "creates customer with driver license and images successfully" do
        put url, params: params, headers: headers
        expect(response).to have_http_status(:ok)

        json = response.parsed_body
        expect(json.dig("status", "message")).to eq("Customer updated successfully")
        expect(json.dig("data", "drive", "customer", "first_name")).to eq("Sarah")
        expect(json.dig("data", "drive", "customer", "driver_license")).to be_present
        expect(json.dig("data", "drive", "customer", "driver_license", "licence_number")).to eq("**********")
        expect(json.dig("data", "drive", "customer", "driver_license", "front_image_url")).to be_present
        expect(json.dig("data", "drive", "customer", "driver_license", "back_image_url")).to be_present
      end
    end

    context "validation errors" do
      it "returns error when neither customer_uuid nor customer_info provided" do
        put url, params: {}, headers: headers
        expect(response).to have_http_status(:unprocessable_entity)

        json = response.parsed_body
        expect(json.dig("status", "message")).to eq("Either customer_uuid or customer_info must be provided")
      end

      it "returns error when both customer_uuid and customer_info provided" do
        existing_customer = create(:customer, dealership: dealership)
        params = {
          customer_uuid: existing_customer.uuid,
          customer_info: { first_name: "Jane", last_name: "Smith", email: "<EMAIL>", phone_number: "+61423456789" }
        }

        put url, params: params, headers: headers
        expect(response).to have_http_status(:unprocessable_entity)

        json = response.parsed_body
        expect(json.dig("status", "message")).to eq("Cannot provide both customer_uuid and customer_info. Use one or the other")
      end
    end
  end

  path "/api/v1/dealerships/{dealership_uuid}/drives/{uuid}/customer" do
    put "Update customer for drive" do
      tags "Drives"
      consumes "application/json"
      produces "application/json"
      security [ Bearer: [] ]

      parameter name: "Authorization", in: :header, type: :string, required: true, description: "Bearer token"
      parameter name: "Device-ID", in: :header, type: :string, required: true, description: "Device identifier"
      parameter name: :dealership_uuid, in: :path, type: :string, required: true, description: "Dealership UUID"
      parameter name: :uuid, in: :path, type: :string, required: true, description: "Drive UUID"
      parameter name: :update_params, in: :body, schema: {
        oneOf: [
          {
            type: :object,
            properties: {
              customer_uuid: {
                type: :string,
                description: "UUID of an existing customer",
                example: "123e4567-e89b-12d3-a456-************"
              }
            },
            required: [ "customer_uuid" ]
          },
          {
            type: :object,
            properties: {
              customer_info: {
                type: :object,
                properties: {
                  first_name: { type: :string, example: "John" },
                  last_name: { type: :string, example: "Doe" },
                  email: { type: :string, example: "<EMAIL>" },
                  phone_number: { type: :string, example: "+61412345678" },
                  age: { type: :integer, example: 30 },
                  gender: { type: :string, enum: [ "unspecified", "male", "female", "other" ], example: "male" },
                  address_line1: { type: :string, example: "123 Main St" },
                  address_line2: { type: :string, example: "Apt 4B" },
                  suburb: { type: :string, example: "Richmond" },
                  city: { type: :string, example: "Melbourne" },
                  state: { type: :string, example: "VIC" },
                  country: { type: :string, example: "Australia" },
                  postcode: { type: :string, example: "3121" },
                  company_name: { type: :string, example: "ACME Corp" },
                  driver_license: {
                    type: :object,
                    properties: {
                      licence_number: { type: :string, example: "12345678" },
                      expiry_date: { type: :string, format: :date, example: "2025-12-31" },
                      issuing_state: { type: :string, example: "VIC" },
                      issuing_country: { type: :string, example: "AU" },
                      full_name: { type: :string, example: "John Doe" },
                      date_of_birth: { type: :string, format: :date, example: "1990-01-01" },
                      front_image: { type: :string, format: :binary, description: "Front image of driver license" },
                      back_image: { type: :string, format: :binary, description: "Back image of driver license" }
                    }
                  }
                },
                required: [ "first_name", "last_name", "email", "phone_number" ]
              }
            },
            required: [ "customer_info" ]
          }
        ]
      }

      response "200", "Customer updated successfully with existing customer" do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     message: { type: :string, example: "Customer updated successfully" }
                   }
                 },
                 data: {
                   type: :object,
                   properties: {
                     drive: {
                       type: :object,
                       properties: {
                         uuid: { type: :string },
                         customer: {
                           type: :object,
                           properties: {
                             uuid: { type: :string },
                             first_name: { type: :string },
                             last_name: { type: :string },
                             email: { type: :string },
                             phone_number: { type: :string },
                             full_name: { type: :string },
                             driver_license: {
                               type: :object,
                               nullable: true,
                               properties: {
                                 uuid: { type: :string },
                                 licence_number: { type: :string },
                                 expiry_date: { type: :string, format: :date },
                                 issuing_state: { type: :string },
                                 issuing_country: { type: :string },
                                 full_name: { type: :string },
                                 date_of_birth: { type: :string, format: :date },
                                 front_image_url: { type: :string, nullable: true, description: "URL of front image" },
                                 back_image_url: { type: :string, nullable: true, description: "URL of back image" }
                               }
                             }
                           }
                         }
                       }
                     }
                   }
                 }
               }

        let(:test_drive) { create(:drive, dealership: dealership, vehicle: vehicle, sales_person: sales_person) }
        let(:uuid) { test_drive.uuid }
        let(:existing_customer) { create(:customer, dealership: dealership) }
        let(:update_params) { { customer_uuid: existing_customer.uuid } }
        run_test! do |response|
          json = response.parsed_body
          expect(json.dig("status", "message")).to eq("Customer updated successfully")
          expect(json.dig("data", "drive", "customer", "uuid")).to eq(existing_customer.uuid)
        end
      end

      response "200", "Customer updated successfully with new customer and driver license" do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     message: { type: :string, example: "Customer updated successfully" }
                   }
                 },
                 data: {
                   type: :object,
                   properties: {
                     drive: {
                       type: :object,
                       properties: {
                         uuid: { type: :string },
                         customer: {
                           type: :object,
                           properties: {
                             uuid: { type: :string },
                             first_name: { type: :string },
                             last_name: { type: :string },
                             email: { type: :string },
                             phone_number: { type: :string },
                             full_name: { type: :string },
                             driver_license: {
                               type: :object,
                               properties: {
                                 uuid: { type: :string },
                                 licence_number: { type: :string },
                                 expiry_date: { type: :string, format: :date },
                                 issuing_state: { type: :string },
                                 issuing_country: { type: :string },
                                 full_name: { type: :string },
                                 date_of_birth: { type: :string, format: :date },
                                 front_image_url: { type: :string, nullable: true, description: "URL of front image" },
                                 back_image_url: { type: :string, nullable: true, description: "URL of back image" }
                               }
                             }
                           }
                         }
                       }
                     }
                   }
                 }
               }

        let(:test_drive) { create(:drive, dealership: dealership, vehicle: vehicle, sales_person: sales_person) }
        let(:uuid) { test_drive.uuid }
        let(:update_params) do
          {
            customer_info: {
              first_name: "Robert",
              last_name: "Johnson",
              email: "<EMAIL>",
              phone_number: "+61434567890",
              gender: "male",
              driver_license: {
                licence_number: "**********",
                expiry_date: "2025-12-31",
                issuing_state: "NSW",
                issuing_country: "AU",
                full_name: "Robert Johnson",
                date_of_birth: "1985-05-15"
              }
            }
          }
        end
        run_test! do |response|
          json = response.parsed_body
          expect(json.dig("status", "message")).to eq("Customer updated successfully")
          expect(json.dig("data", "drive", "customer", "first_name")).to eq("Robert")
          expect(json.dig("data", "drive", "customer", "driver_license")).to be_present
          expect(json.dig("data", "drive", "customer", "driver_license", "licence_number")).to eq("**********")
        end
      end

      response "422", "Validation error" do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     message: { type: :string }
                   }
                 }
               }

        let(:test_drive) { create(:drive, dealership: dealership, vehicle: vehicle, sales_person: sales_person) }
        let(:uuid) { test_drive.uuid }
        let(:update_params) { {} }
        run_test! do |response|
          expect(response.status).to eq(422)
          expect(response.parsed_body.dig("status", "message")).to eq("Either customer_uuid or customer_info must be provided")
        end
      end

      response "404", "Drive not found" do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     message: { type: :string }
                   }
                 }
               }

        let(:uuid) { "non-existent-uuid" }
        let(:update_params) { { customer_uuid: "some-uuid" } }
        run_test!
      end

      response "401", "Unauthorized" do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     message: { type: :string }
                   }
                 }
               }

        let(:test_drive) { create(:drive, dealership: dealership, vehicle: vehicle, sales_person: sales_person) }
        let(:uuid) { test_drive.uuid }
        let(:update_params) { { customer_uuid: "some-uuid" } }
        let(:Authorization) { "Bearer invalid_token" }
        run_test!
      end

      response "404", "Customer not found" do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     message: { type: :string }
                   }
                 }
               }

        let(:test_drive) { create(:drive, dealership: dealership, vehicle: vehicle, sales_person: sales_person) }
        let(:uuid) { test_drive.uuid }
        let(:update_params) { { customer_uuid: "non-existent-uuid" } }
        run_test! do |response|
          expect(response.status).to eq(404)
          expect(response.parsed_body.dig("status", "message")).to include("Customer not found")
        end
      end
    end
  end
end
