require "rails_helper"
require "swagger_helper"

RSpec.describe "Api::V1::UsersController#refresh", type: :request do
  include_context "users_api_shared_context"

  path "/api/v1/auth/refresh" do
    post "Refresh Access Token" do
      tags "Authentication"
      consumes "application/json"
      produces "application/json"

      parameter name: :payload, in: :body, schema: {
        type: :object,
        required: %w[refresh_token device_id],
        properties: {
          refresh_token: { type: :string, example: "valid.refresh.token" },
          device_id: { type: :string, example: "device123" }
        }
      }

      response "200", "Token refreshed successfully" do
        let(:payload) do
          {
            refresh_token: "valid.refresh.token",
            device_id: device_registration.device_id
          }
        end

        before do
          service = instance_double(Auth::TokenService)
          allow(Auth::TokenService).to receive(:new).and_return(service)
          allow(service).to receive(:refresh_access_token!).with("valid.refresh.token")
            .and_return({ access_token: "new.access.token" })
        end

        run_test! do |response|
          json = JSON.parse(response.body)
          expect(json.dig("data", "access_token")).to eq("new.access.token")
          expect(json.dig("status", "message")).to eq("Token refreshed successfully")
        end
      end

      response "401", "Invalid refresh token" do
        let(:payload) do
          {
            refresh_token: "invalid.token",
            device_id: device_registration.device_id
          }
        end

        before do
          service = instance_double(Auth::TokenService)
          allow(Auth::TokenService).to receive(:new).and_return(service)
          allow(service).to receive(:refresh_access_token!).with("invalid.token")
            .and_raise(Errors::InvalidRefreshToken, "Invalid refresh token")
        end

        run_test! do |response|
          json = JSON.parse(response.body)
          expect(response.status).to eq(401)
          expect(json.dig("status", "message")).to include("Invalid refresh token")
        end
      end

      response "422", "Missing parameters" do
        let(:payload) { { refresh_token: nil, device_id: "" } }

        run_test! do |response|
          json = JSON.parse(response.body)
          expect(response.status).to eq(422)
          expect(json.dig("status", "message")).to eq("Device ID and refresh token are required")
        end
      end
    end
  end

  describe "POST /api/v1/auth/refresh" do
    subject { post "/api/v1/auth/refresh", params: params, as: :json }

    context "with valid refresh token and device_id" do
      let(:params) do
        {
          refresh_token: "valid.refresh.token",
          device_id: device_registration.device_id
        }
      end

      before do
        service = instance_double(Auth::TokenService)
        allow(Auth::TokenService).to receive(:new).and_return(service)
        allow(service).to receive(:refresh_access_token!).with("valid.refresh.token")
          .and_return({ access_token: "new.access.token" })
      end

      it "returns refreshed access token" do
        subject
        expect(response).to have_http_status(:ok)
        expect(response.parsed_body.dig("data", "access_token")).to eq("new.access.token")
      end
    end

    context "with invalid refresh token" do
      let(:params) do
        {
          refresh_token: "invalid.token",
          device_id: device_registration.device_id
        }
      end

      before do
        service = instance_double(Auth::TokenService)
        allow(Auth::TokenService).to receive(:new).and_return(service)
        allow(service).to receive(:refresh_access_token!).with("invalid.token")
          .and_raise(Errors::InvalidRefreshToken, "Invalid refresh token")
      end

      it "returns unauthorized" do
        subject
        expect(response).to have_http_status(:unauthorized)
        expect(response.parsed_body.dig("status", "message")).to include("Invalid refresh token")
      end
    end

    context "with missing params" do
      let(:params) { { device_id: "" } }

      it "returns unprocessable entity" do
        subject
        expect(response).to have_http_status(:unprocessable_entity)
        expect(response.parsed_body.dig("status", "message")).to eq("Device ID and refresh token are required")
      end
    end
  end
end
