require "rails_helper"
require "swagger_helper"

RSpec.describe "Api::V1::UsersController#resend_otp", type: :request do
  include_context "users_api_shared_context"

  let(:temporary_token) { "valid.temp.token" }
  let(:channel) { "email" }

  path "/api/v1/auth/resend-otp" do
    post "Resend OTP" do
      tags "Authentication"
      consumes "application/json"
      produces "application/json"

      parameter name: :payload, in: :body, schema: {
        type: :object,
        required: %w[temporary_token method],
        properties: {
          temporary_token: { type: :string, example: "valid.temp.token" },
          method: { type: :string, enum: %w[email sms], example: "email" }
        }
      }

      response "200", "OTP resent successfully" do
        let(:payload) { { temporary_token: temporary_token, method: channel } }

        before do
          allow_any_instance_of(Auth::TokenService).to receive(:decode_token).with(temporary_token).and_return(user)
          allow(OtpService).to receive(:new).with(user).and_return(
            instance_double(OtpService, generate_and_send_otp: true)
          )
        end

        run_test! do |response|
          json = JSON.parse(response.body)
          expect(json.dig("status", "message")).to eq("OTP sent successfully.")
          expect(json.dig("data", "two_factor_method")).to eq(channel)
        end
      end

      response "401", "Invalid token" do
        let(:payload) { { temporary_token: "invalid.token", method: "email" } }

        before do
          allow_any_instance_of(Auth::TokenService).to receive(:decode_token)
            .with("invalid.token")
            .and_raise(Errors::InvalidToken, "Invalid token")
        end

        run_test! do |response|
          expect(response.status).to eq(401)
          expect(response.parsed_body.dig("status", "message")).to eq("Invalid token")
        end
      end

      response "422", "Missing method param" do
        let(:payload) { { temporary_token: temporary_token, method: nil } }

        before do
          allow_any_instance_of(Auth::TokenService).to receive(:decode_token).with(temporary_token).and_return(user)
        end

        run_test! do |response|
          expect(response.status).to eq(422)
          expect(response.parsed_body.dig("status", "message")).to eq("Method is required")
        end
      end
    end
  end

  describe "POST /api/v1/auth/resend-otp" do
    subject { post "/api/v1/auth/resend-otp", params: params.to_json, headers: headers }

    let(:headers) { { "CONTENT_TYPE" => "application/json" } }

    context "with valid temporary token and method" do
      let(:params) { { temporary_token: temporary_token, method: channel } }

      before do
        allow_any_instance_of(Auth::TokenService).to receive(:decode_token).with(temporary_token).and_return(user)
        allow(OtpService).to receive(:new).with(user).and_return(
          instance_double(OtpService, generate_and_send_otp: true)
        )
      end

      it "returns success response" do
        subject
        expect(response).to have_http_status(:ok)
        expect(response.parsed_body.dig("status", "message")).to eq("OTP sent successfully.")
        expect(response.parsed_body.dig("data", "two_factor_method")).to eq(channel)
      end
    end

    context "when token is invalid" do
      let(:params) { { temporary_token: "invalid.token", method: "email" } }

      before do
        allow_any_instance_of(Auth::TokenService).to receive(:decode_token)
          .with("invalid.token")
          .and_raise(Errors::InvalidToken, "Invalid token")
      end

      it "returns 401 unauthorized" do
        subject
        expect(response).to have_http_status(:unauthorized)
        expect(response.parsed_body.dig("status", "message")).to eq("Invalid token")
      end
    end

    context "when method param is missing" do
      let(:params) { { temporary_token: temporary_token } }

      before do
        allow_any_instance_of(Auth::TokenService).to receive(:decode_token).with(temporary_token).and_return(user)
      end

      it "returns 422 unprocessable entity" do
        subject
        expect(response).to have_http_status(:unprocessable_entity)
        expect(response.parsed_body.dig("status", "message")).to eq("Method is required")
      end
    end
  end
end
