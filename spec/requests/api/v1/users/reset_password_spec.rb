require "rails_helper"
require "swagger_helper"

RSpec.describe "Api::V1::UsersController#reset_password", type: :request do
  include_context "users_api_shared_context"

  let(:temporary_token) { Auth::TokenService.new(nil, user).generate_temporary_token.first }
  let(:new_password) { "NewStrong@123" }

  path "/api/v1/auth/reset-password" do
    post "Reset Password" do
      tags "Authentication"
      consumes "application/json"
      produces "application/json"

      parameter name: :payload, in: :body, schema: {
        type: :object,
        required: %w[temporary_token new_password],
        properties: {
          temporary_token: { type: :string, example: "valid.token" },
          new_password: { type: :string, example: "NewPass@123" }
        }
      }

      response "200", "password reset" do
        let(:payload) { { temporary_token: temporary_token, new_password: new_password } }

        before do
          allow_any_instance_of(Auth::TokenService).to receive(:decode_token).and_return(user)
          allow(user).to receive(:valid_password?).with(new_password).and_return(false)
          allow(user).to receive(:update!).with(password: new_password).and_return(true)
          allow(user).to receive(:mark_password_as_changed!).and_return(true)
        end

        run_test! do |response|
          json = JSON.parse(response.body)
          expect(json.dig("status", "message")).to eq("Password reset successful.")
        end
      end

      response "422", "missing or invalid inputs" do
        let(:payload) { { temporary_token: "", new_password: "" } }

        run_test! do |response|
          expect(response).to have_http_status(:unprocessable_entity)
        end
      end
    end
  end

  describe "POST /api/v1/auth/reset-password" do
    subject do
      post "/api/v1/auth/reset-password", params: {
        temporary_token: token,
        new_password: password
      }
    end

    before do
      allow_any_instance_of(Auth::TokenService).to receive(:decode_token).and_return(user)
    end

    context "with valid token and password" do
      let(:token) { temporary_token }
      let(:password) { new_password }

      before do
        allow(user).to receive(:valid_password?).with(password).and_return(false)
        allow(user).to receive(:update!).with(password: password).and_return(true)
        allow(user).to receive(:mark_password_as_changed!).and_return(true)
      end

      it "resets password successfully" do
        subject
        expect(response).to have_http_status(:ok)
        expect(response.parsed_body.dig("status", "message")).to eq("Password reset successful.")
      end
    end

    context "when missing token or password" do
      context "missing token" do
        let(:token) { nil }
        let(:password) { new_password }

        it "returns 422" do
          subject
          expect(response).to have_http_status(:unprocessable_entity)
        end
      end

      context "missing password" do
        let(:token) { temporary_token }
        let(:password) { nil }

        it "returns 422" do
          subject
          expect(response).to have_http_status(:unprocessable_entity)
        end
      end
    end

    context "with invalid or expired token" do
      let(:token) { "bad.token" }
      let(:password) { new_password }

      it "returns 401 for invalid token" do
        allow_any_instance_of(Auth::TokenService).to receive(:decode_token).and_raise(Errors::InvalidToken)
        subject
        expect(response).to have_http_status(:unauthorized)
      end

      it "returns 401 for expired token" do
        allow_any_instance_of(Auth::TokenService).to receive(:decode_token).and_raise(Errors::TokenExpired)
        subject
        expect(response).to have_http_status(:unauthorized)
      end
    end

    context "when new password is same as old" do
      let(:token) { temporary_token }
      let(:password) { new_password }

      before do
        allow(user).to receive(:valid_password?).with(password).and_return(true)
      end

      it "returns 422 with message" do
        subject
        expect(response).to have_http_status(:unprocessable_entity)
        expect(response.parsed_body.dig("status", "message")).to eq("New password cannot be the same as the current password")
      end
    end

    context "when password fails strength validation" do
      let(:token) { temporary_token }
      let(:password) { "weak" }

      it "returns 422 with error message" do
        subject
        expect(response).to have_http_status(:unprocessable_entity)
        expect(response.parsed_body.dig("status", "message")).to include(
          "Password requirement not met. Please use: 1 uppercase, 1 lowercase, 1 digit and 1 special character"
        )
      end
    end
  end
end
