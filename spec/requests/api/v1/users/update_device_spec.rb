require "rails_helper"
require "swagger_helper"

RSpec.describe "Api::V1::UsersController#update_device", type: :request do
  include_context "users_api_shared_context"
  let(:device_registration) { create(:device_registration, user: user, device_id: 'device-id-123', app_version: "1.0.0", device_os_version: "1.2.3") }

  path "/api/v1/devices" do
    patch "Update device details" do
      tags "User Devices"
      consumes "application/json"
      produces "application/json"
      security [ Bearer: [] ]

      parameter name: "Authorization", in: :header, type: :string, required: true, description: "Bearer token"
      parameter name: "Device-ID", in: :header, type: :string, required: true, description: "Device identifier"

      parameter name: :device_params, in: :body, schema: {
        type: :object,
        properties: {
          device_os: { type: :string, example: "ios" },
          device_os_version: { type: :string, example: "1.2.3" },
          app_version: { type: :string, example: "1.0.0" },
          app_build_number: { type: :string, example: "1.0.0" },
          fcm_token: { type: :string, example: "fcm-token-123" },
          device_name: { type: :string, example: "IPhone 15" }
        }
      }

      response "200", "Device details updated successfully" do
        let(:device_params) do
          {
            device_os: "ios",
            device_os_version: "17.2.1",
            app_version: "2.0.0",
            app_build_number: "200",
            fcm_token: "fcm-token-123",
            device_name: "iPhone 15 Pro"
          }
        end

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data.dig("status", "message")).to eq("Device details updated successfully")
          expect(data.dig("data", "device", "device_os")).to eq("ios")
          expect(data.dig("data", "device", "device_os_version")).to eq("17.2.1")
          expect(data.dig("data", "device", "app_version")).to eq("2.0.0")
          expect(data.dig("data", "device", "app_build_number")).to eq("200")

          # Verify database was updated
          device_registration.reload
          expect(device_registration.device_os).to eq("ios")
          expect(device_registration.device_os_version).to eq("17.2.1")
          expect(device_registration.app_version).to eq("2.0.0")
          expect(device_registration.app_build_number).to eq("200")
          expect(device_registration.fcm_token).to eq("fcm-token-123")
          expect(device_registration.device_name).to eq("iPhone 15 Pro")
        end
      end

      response "401", "Unauthorized" do
        let(:Authorization) { "Bearer invalid_token" }
        let(:device_params) { { device_os: "ios" } }

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data.dig("status", "message")).to include("Invalid token")
        end
      end

      response "422", "Validation failed" do
        let(:device_params) { { device_os: "" } }

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(response.status).to eq(422)
          expect(data.dig("status", "message")).to include("can't be blank")
        end
      end
    end
  end

  describe "POST /api/v1/devices" do
    subject { post "/api/v1/devices", params: params, headers: headers, as: :json }
    let(:params) do
      {
        device_os: "ios",
        device_os_version: "17.2.1",
        app_version: "2.0.0",
        app_build_number: "200",
        fcm_token: "fcm-token-123",
        device_name: "iPhone 15 Pro"
      }
    end

    context "when updating an existing device" do
      it "updates the existing device" do
        subject
        expect(response).to have_http_status(:ok)
        json = response.parsed_body

        device_registration.reload
        expect(device_registration.app_version).to eq("2.0.0")
        expect(device_registration.device_os_version).to eq("17.2.1")
        expect(json.dig("data", "device", "device_id")).to eq("device-id-123")
      end
    end

    context "when missing required parameters" do
      let(:params) do
        {
          device_name: "iPhone 15 Pro"
        }
      end

      it "returns unprocessable entity" do
        subject

        expect(response).to have_http_status(:unprocessable_entity)
        expect(response.parsed_body.dig("status", "message")).to include("can't be blank")
      end
    end

    context "when unauthorized" do
      let(:headers) { {} }

      it "returns unauthorized" do
        subject

        expect(response).to have_http_status(:unauthorized)
      end
    end
  end
end
