require "rails_helper"
require "swagger_helper"

RSpec.describe "Api::V1::UserInvitationsController#create", type: :request do
  include_context "users_api_shared_context"

  let(:inviter) { create(:user) }
  let(:dealership) { create(:dealership) }
  let!(:admin_membership) { create(:user_dealership, :admin, user: inviter, dealership: dealership) }
  let(:device_registration) { create(:device_registration, user: inviter) }

  path "/api/v1/user_invitations" do
    post "Invite a user" do
      tags "User Invitations"
      consumes "application/json"
      produces "application/json"
      security [ Bearer: [] ]

      parameter name: "Authorization", in: :header, type: :string, required: true, example: "Bearer <token>"
      parameter name: "Device-ID", in: :header, type: :string, required: true, example: "device-123"
      parameter name: :payload, in: :body, schema: {
        type: :object,
        required: [ "user" ],
        properties: {
          user: {
            type: :object,
            required: %w[email first_name last_name phone user_type dealership_uuid role_type],
            properties: {
              email: { type: :string, example: "<EMAIL>" },
              first_name: { type: :string, example: "<PERSON>" },
              last_name: { type: :string, example: "<PERSON>" },
              phone: { type: :string, example: "+61412345678" },
              user_type: { type: :string, example: "dealership_user" },
              dealership_uuid: { type: :string, example: "uuid-of-dealership" },
              role_type: { type: :string, example: "dealership_admin" }
            }
          }
        }
      }

      response "200", "User invitation sent successfully" do
        let(:payload) do
          {
            user: {
              email: "<EMAIL>",
              first_name: "Jane",
              last_name: "Smith",
              phone: "+61412345678",
              user_type: "dealership_user",
              dealership_uuid: dealership.uuid,
              role_type: "sales_person"
            }
          }
        end

        before do
          allow_any_instance_of(UserInvitationService).to receive(:invite_user).and_call_original
        end

        run_test! do |response|
          json = JSON.parse(response.body)
          expect(json.dig("status", "message")).to eq("User invitation sent successfully")
          expect(json.dig("data", "user")).to be_present
        end
      end

      response "422", "Invalid input" do
        let(:payload) { { user: { email: "", first_name: "", last_name: "", phone: "", user_type: "", dealership_uuid: "", role_type: "" } } }

        run_test! do |response|
          expect(response).to have_http_status(:unprocessable_entity)
        end
      end

      response "403", "Forbidden dealership_admin invite when inviter is a dealership user" do
        let(:non_admin_inviter) { create(:user) }
        let(:dealership) { create(:dealership) }
        let!(:non_admin_membership) { create(:user_dealership, user: non_admin_inviter, dealership: dealership, role: :dealership_admin) }
        let(:device_registration) { create(:device_registration, user: non_admin_inviter) }
        let(:payload) do
          {
            user: {
              email: "<EMAIL>",
              first_name: "Jane",
              last_name: "Smith",
              phone: "+61412345678",
              user_type: "dealership_user",
              dealership_uuid: dealership.uuid,
              role_type: "dealership_admin"
            }
          }
        end

        run_test! do |response|
          expect(response).to have_http_status(:forbidden)
          json = JSON.parse(response.body)
          expect(json.dig("status", "message")).to eq("Only super admins and staff can assign dealership admin role")
        end
      end

      response "403", "Forbidden to invite from other dealership" do
        let(:non_admin_inviter) { create(:user) }
        let(:dealership2) { create(:dealership) }
        let!(:non_admin_membership) { create(:user_dealership, user: non_admin_inviter, dealership: dealership2, role: :dealership_admin) }
        let(:device_registration) { create(:device_registration, user: non_admin_inviter) }
        let(:payload) do
          {
            user: {
              email: "<EMAIL>",
              first_name: "Jane",
              last_name: "Smith",
              phone: "+61412345678",
              user_type: "dealership_user",
              dealership_uuid: dealership.uuid,
              role_type: "sales_person"
            }
          }
        end

        run_test! do |response|
          expect(response).to have_http_status(:forbidden)
          json = JSON.parse(response.body)
          expect(json.dig("status", "message")).to eq("Users can only invite users to dealerships where they are an admin")
        end
      end

      response "401", "Unauthorized when token is invalid" do
        let(:payload) do
          {
            user: {
              email: "<EMAIL>",
              first_name: "Jane",
              last_name: "Smith",
              phone: "+61412345678",
              user_type: "dealership_user",
              dealership_uuid: dealership.uuid,
              role_type: "sales_person"
            }
          }
          end
        let(:Authorization) { "Bearer invalid_token" }

        run_test! do |response|
          expect(response).to have_http_status(:unauthorized)
        end
      end

      response "401", "Unauthorized when Device-ID is missing" do
        let(:payload) do
          {
            user: {
              email: "<EMAIL>",
              first_name: "Jane",
              last_name: "Smith",
              phone: "+61412345678",
              user_type: "dealership_user",
              dealership_uuid: dealership.uuid,
              role_type: "sales_person"
            }
          }
        end
        let(:"Device-ID") { "" }

        run_test! do |response|
          expect(response).to have_http_status(:unauthorized)
        end
      end

      response "422", "Invalid email format" do
        let(:payload) do
          {
            user: {
              email: "invalid-email",
              first_name: "Jane",
              last_name: "Smith",
              phone: "+61412345678",
              user_type: "dealership_user",
              dealership_uuid: dealership.uuid,
              role_type: "sales_person"
            }
          }
        end

        run_test! do |response|
          expect(response).to have_http_status(:unprocessable_entity)
          json = JSON.parse(response.body)
          expect(json.dig("status", "message")).to include("is not a valid email")
        end
      end

      response "422", "Role is required when inviting a dealership user" do
        let(:payload) do
          {
            user: {
              email: "<EMAIL>",
              first_name: "Jane",
              last_name: "Smith",
              phone: "+61412345678",
              user_type: "dealership_user",
              dealership_uuid: dealership.uuid
            }
          }
        end

        run_test! do |response|
          expect(response).to have_http_status(:unprocessable_entity)
          json = JSON.parse(response.body)
          expect(json.dig("status", "message")).to include("Role is required when inviting a dealership user")
        end
      end

      response "422", "Invalid phone format" do
        let(:payload) do
          {
            user: {
              email: "<EMAIL>",
              first_name: "Jane",
              last_name: "Smith",
              phone: "invalid-phone",
              user_type: "dealership_user",
              dealership_uuid: dealership.uuid,
              role_type: "sales_person"
            }
          }
        end

        run_test! do |response|
          expect(response).to have_http_status(:unprocessable_entity)
          json = JSON.parse(response.body)
          expect(json.dig("status", "message")).to include("Phone is invalid")
        end
      end

      response "404", "Dealership not found" do
        let(:payload) do
          {
            user: {
              email: "<EMAIL>",
              first_name: "Jane",
              last_name: "Smith",
              phone: "+61412345678",
              user_type: "dealership_user",
              dealership_uuid: "non-existent-uuid",
              role_type: "sales_person"
            }
          }
        end

        run_test! do |response|
          expect(response).to have_http_status(:not_found)
          json = JSON.parse(response.body)
          expect(json.dig("status", "message")).to eq("Dealership not found")
        end
      end

      response "422", "User already exists with this email" do
        let!(:existing_user) { create(:user, email: "<EMAIL>") }
        let(:payload) do
          {
            user: {
              email: "<EMAIL>",
              first_name: "Jane",
              last_name: "Smith",
              phone: "+61412345678",
              user_type: "dealership_user",
              dealership_uuid: dealership.uuid,
              role_type: "sales_person"
            }
          }
        end

        run_test! do |response|
          expect(response).to have_http_status(:unprocessable_entity)
          json = JSON.parse(response.body)
          expect(json.dig("status", "message")).to include("Email has already been taken")
        end
      end

      response "422", "Invalid user_type" do
        let(:payload) do
          {
            user: {
              email: "<EMAIL>",
              first_name: "Jane",
              last_name: "Smith",
              phone: "+61412345678",
              user_type: "invalid_type",
              dealership_uuid: dealership.uuid,
              role_type: "sales_person"
            }
          }
        end

        run_test! do |response|
          expect(response).to have_http_status(:unprocessable_entity)
          json = JSON.parse(response.body)
          expect(json.dig("status", "message")).to include("Invalid user type")
        end
      end

      response "422", "Dealership UUID not provided" do
        let(:payload) do
          {
            user: {
              email: "<EMAIL>",
              first_name: "Jane",
              last_name: "Smith",
              phone: "+61412345678",
              user_type: "dealership_user",
              role_type: "sales_person"
            }
          }
        end

        run_test! do |response|
          expect(response).to have_http_status(:unprocessable_entity)
          json = JSON.parse(response.body)
          expect(json.dig("status", "message")).to include("Dealership UUID is required")
        end
      end

      response "422", "Invalid role_type" do
        let(:payload) do
          {
            user: {
              email: "<EMAIL>",
              first_name: "Jane",
              last_name: "Smith",
              phone: "+61412345678",
              user_type: "dealership_user",
              dealership_uuid: dealership.uuid,
              role_type: "invalid_role"
            }
          }
        end

        run_test! do |response|
          expect(response).to have_http_status(:unprocessable_entity)
          json = JSON.parse(response.body)
          expect(json.dig("status", "message")).to include("Invalid role")
        end
      end

      response "400", "Missing required user parameter" do
        let(:payload) { {} }

        run_test! do |response|
          expect(response).to have_http_status(:bad_request)
          json = JSON.parse(response.body)
          expect(json.dig("status", "message")).to include("param is missing")
        end
      end

      response "500", "Internal server error when service fails" do
        let(:payload) do
          {
            user: {
              email: "<EMAIL>",
              first_name: "Jane",
              last_name: "Smith",
              phone: "+61412345678",
              user_type: "dealership_user",
              dealership_uuid: dealership.uuid,
              role_type: "sales_person"
            }
          }
        end

        before do
          allow_any_instance_of(UserInvitationService).to receive(:invite_user).and_raise(StandardError.new("Service error"))
        end

        run_test! do |response|
          expect(response).to have_http_status(:internal_server_error)
          json = JSON.parse(response.body)
          expect(json.dig("status", "message")).to eq("Service error")
        end
      end

      response "200", "Successfully invite staff role" do
        let(:payload) do
          {
            user: {
              email: "<EMAIL>",
              first_name: "John",
              last_name: "Manager",
              phone: "+61412345679",
              user_type: "dealership_user",
              dealership_uuid: dealership.uuid,
              role_type: "staff"
            }
          }
        end

        run_test! do |response|
          json = JSON.parse(response.body)
          expect(json.dig("status", "message")).to eq("User invitation sent successfully")
          expect(json.dig("data", "user", "dealership_role")).to eq("staff")
        end
      end

      response "200", "Successfully invite super admin role" do
        let(:inviter) { create(:user, :super_admin) }
        let(:device_registration) { create(:device_registration, user: inviter) }
        let(:payload) do
          {
            user: {
              email: "<EMAIL>",
              first_name: "John",
              last_name: "Manager",
              phone: "+61412345679",
              user_type: "super_admin"
            }
          }
        end

        run_test! do |response|
          json = JSON.parse(response.body)
          expect(json.dig("status", "message")).to eq("User invitation sent successfully")
          expect(json.dig("data", "user", "user_type")).to eq("super_admin")
        end
      end

      response "403", "Staff member can't invite non dealership_users" do
        let(:inviter) { create(:user, :staff) }
        let(:device_registration) { create(:device_registration, user: inviter) }
        let(:payload) do
          {
            user: {
              email: "<EMAIL>",
              first_name: "John",
              last_name: "Manager",
              phone: "+61412345679",
              user_type: "staff"
            }
          }
        end

        run_test! do |response|
          expect(response).to have_http_status(:forbidden)
          json = JSON.parse(response.body)
          expect(json.dig("status", "message")).to include("Staff members can only invite dealership users")
        end
      end

      response "403", "Dealership users can only invite other dealership users" do
        let(:inviter) { create(:user) }
        let(:device_registration) { create(:device_registration, user: inviter) }
        let(:payload) do
          {
            user: {
              email: "<EMAIL>",
              first_name: "John",
              last_name: "Manager",
              phone: "+61412345679",
              user_type: "staff"
            }
          }
        end

        run_test! do |response|
          expect(response).to have_http_status(:forbidden)
          json = JSON.parse(response.body)
          expect(json.dig("status", "message")).to include("Dealership users can only invite other dealership users")
        end
      end

      response "200", "Successfully invite staff role" do
        let(:inviter) { create(:user, :super_admin) }
        let(:device_registration) { create(:device_registration, user: inviter) }
        let(:payload) do
          {
            user: {
              email: "<EMAIL>",
              first_name: "John",
              last_name: "Manager",
              phone: "+61412345679",
              user_type: "staff"
            }
          }
        end

        run_test! do |response|
          json = JSON.parse(response.body)
          expect(json.dig("status", "message")).to eq("User invitation sent successfully")
          expect(json.dig("data", "user", "user_type")).to eq("staff")
        end
      end

      response "422", "Dealership UUID with a non dealership user" do
        let(:inviter) { create(:user, :super_admin) }
        let(:device_registration) { create(:device_registration, user: inviter) }
        let(:payload) do
          {
            user: {
              email: "<EMAIL>",
              first_name: "John",
              last_name: "Manager",
              phone: "+61412345679",
              user_type: "staff",
              dealership_uuid: dealership.uuid,
              role_type: "staff"
            }
          }
        end

        run_test! do |response|
          expect(response).to have_http_status(:unprocessable_entity)
          json = JSON.parse(response.body)
          expect(json.dig("status", "message")).to include("Dealership UUID should not be provided")
        end
      end

      response "200", "Successfully invite with international phone number" do
        let(:payload) do
          {
            user: {
              email: "<EMAIL>",
              first_name: "International",
              last_name: "User",
              phone: "+************",
              user_type: "dealership_user",
              dealership_uuid: dealership.uuid,
              role_type: "sales_person"
            }
          }
        end

        run_test! do |response|
          json = JSON.parse(response.body)
          expect(json.dig("status", "message")).to eq("User invitation sent successfully")
          expect(json.dig("data", "user", "phone")).to eq("+************")
        end
      end
    end
  end
end
