---
openapi: 3.0.1
info:
  title: API V1
  version: v1
paths:
  "/api/v1/dealerships/{dealership_uuid}/bookings/{uuid}/cancel":
    patch:
      summary: Cancel a booking
      tags:
      - Bookings
      description: Cancels a booking
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        schema:
          type: string
      - name: dealership_uuid
        in: path
        required: true
        schema:
          type: string
      - name: uuid
        in: path
        required: true
        schema:
          type: string
      responses:
        '200':
          description: booking cancelled
        '422':
          description: missing cancel reason
        '404':
          description: booking not found
        '401':
          description: unauthorized - invalid device
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                cancel_reason:
                  type: string
              required:
              - cancel_reason
  "/api/v1/dealerships/{dealership_uuid}/bookings":
    post:
      summary: Creates a booking
      tags:
      - Bookings
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Bearer token
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device ID
        schema:
          type: string
      - name: dealership_uuid
        in: path
        required: true
        description: Dealership UUID
        schema:
          type: string
      responses:
        '201':
          description: test drive booking created successfully
        '422':
          description: invalid time sequence (pickup >= return)
        '401':
          description: unauthorized - expired token
        '404':
          description: sales person not found
      requestBody:
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                vehicle_uuid:
                  type: string
                drive_type:
                  type: string
                  enum:
                  - test_drive_booking
                  - loan_booking
                expected_pickup_datetime:
                  type: string
                  format: date-time
                expected_return_datetime:
                  type: string
                  format: date-time
                sales_person_uuid:
                  type: string
                customer_uuid:
                  type: string
                notes:
                  type: string
                customer_info:
                  type: object
                  properties:
                    first_name:
                      type: string
                    last_name:
                      type: string
                    email:
                      type: string
                    phone_number:
                      type: string
                    address_line1:
                      type: string
                    city:
                      type: string
                    state:
                      type: string
                    postcode:
                      type: string
                    age:
                      type: integer
                    gender:
                      type: string
                      enum:
                      - unspecified
                      - male
                      - female
                      - other
                    company_name:
                      type: string
                    suburb:
                      type: string
                    address_line2:
                      type: string
                    country:
                      type: string
                    driver_license:
                      type: object
                      properties:
                        licence_number:
                          type: string
                        expiry_date:
                          type: string
                        issuing_state:
                          type: string
                        issuing_country:
                          type: string
                        full_name:
                          type: string
                        date_of_birth:
                          type: string
                        front_image:
                          type: string
                          format: binary
                        back_image:
                          type: string
                          format: binary
              required:
              - vehicle_uuid
              - drive_type
              - expected_pickup_datetime
              - expected_return_datetime
              - sales_person_uuid
    get:
      summary: Get list of bookings
      tags:
      - Bookings
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        schema:
          type: string
      - name: dealership_uuid
        in: path
        required: true
        schema:
          type: string
      - name: status
        in: query
        required: false
        enum:
        - scheduled
        - completed
        - cancelled
        description: "Filter bookings by status:\n * `scheduled` \n * `completed`
          \n * `cancelled` \n "
        schema:
          type: string
      - name: drive_type
        in: query
        required: false
        enum:
        - test_drive_booking
        - loan_booking
        description: 'Filter bookings by drive type: test_drive_booking, loan_booking'
        schema:
          type: string
      - name: vehicle_uuid
        in: query
        required: false
        description: Filter bookings by vehicle UUID
        schema:
          type: string
      - name: sales_person_uuid
        in: query
        required: false
        description: Filter bookings by sales person UUID
        schema:
          type: string
      - name: start_date
        in: query
        required: false
        format: date
        description: Filter bookings from this date
        schema:
          type: string
      - name: end_date
        in: query
        required: false
        format: date
        description: Filter bookings until this date
        schema:
          type: string
      - name: page
        in: query
        required: false
        description: 'Page number for pagination (default: 1)'
        schema:
          type: integer
      - name: per_page
        in: query
        required: false
        description: 'Number of items per page (default: 20, max: 100)'
        schema:
          type: integer
      responses:
        '200':
          description: bookings with pagination
        '401':
          description: unauthorized - Invalid device
  "/api/v1/dealerships/{dealership_uuid}/bookings/{uuid}":
    get:
      summary: Retrieve a specific booking by UUID
      tags:
      - Bookings
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        schema:
          type: string
      - name: uuid
        in: path
        required: true
        schema:
          type: string
      - name: dealership_uuid
        in: path
        required: true
        schema:
          type: string
      responses:
        '200':
          description: booking found
        '404':
          description: booking not found
        '401':
          description: unauthorized - Invalid device
    put:
      summary: Update an existing booking
      tags:
      - Bookings
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        schema:
          type: string
      - name: dealership_uuid
        in: path
        required: true
        schema:
          type: string
      - name: uuid
        in: path
        required: true
        schema:
          type: string
      responses:
        '200':
          description: booking updated successfully
        '422':
          description: invalid input (pickup before return)
        '401':
          description: unauthorized - Invalid device
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                vehicle_uuid:
                  type: string
                sales_person_uuid:
                  type: string
                expected_pickup_datetime:
                  type: string
                  format: date-time
                expected_return_datetime:
                  type: string
                  format: date-time
                notes:
                  type: string
              description: At least one of the fields must be provided. All fields
                are optional but at least one is required.
  "/api/v1/brands":
    get:
      summary: Get all brands
      tags:
      - Brands
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Bearer token
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device ID
        schema:
          type: string
      responses:
        '200':
          description: brands retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 200
                      message:
                        type: string
                        example: Brands retrieved successfully
                    required:
                    - code
                    - message
                  data:
                    type: object
                    properties:
                      brands:
                        type: array
                        items:
                          type: object
                          properties:
                            uuid:
                              type: string
                              example: 123e4567-e89b-12d3-a456-************
                            name:
                              type: string
                              example: Toyota
                            logo_url:
                              type: string
                              nullable: true
                              example: https://example.com/logo.png
                          required:
                          - uuid
                          - name
                          - logo_url
                    required:
                    - brands
                required:
                - status
                - data
        '401':
          description: unauthorized - missing device ID
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 401
                      message:
                        type: string
                        example: Invalid device
                    required:
                    - code
                    - message
                required:
                - status
  "/api/v1/dealerships/{dealership_uuid}/customers":
    get:
      summary: Get customers list with optional search
      tags:
      - Customers
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Bearer token
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device identifier
        schema:
          type: string
      - name: dealership_uuid
        in: path
        required: true
        description: Dealership UUID
        schema:
          type: string
      - name: page
        in: query
        required: false
        description: 'Page number (default: 1)'
        schema:
          type: integer
      - name: per_page
        in: query
        required: false
        description: 'Items per page (default: 20, max: 100)'
        schema:
          type: integer
      responses:
        '200':
          description: Customers retrieved successfully
        '401':
          description: Unauthorized
        '404':
          description: Dealership not found
    post:
      summary: Create customer
      tags:
      - Customers
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Bearer token
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device identifier
        schema:
          type: string
      - name: dealership_uuid
        in: path
        required: true
        description: Dealership UUID
        schema:
          type: string
      responses:
        '200':
          description: Customer created successfully
        '422':
          description: Duplicate email error
        '401':
          description: Unauthorized
        '404':
          description: Dealership not found
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                first_name:
                  type: string
                  description: Customer first name
                last_name:
                  type: string
                  description: Customer last name
                email:
                  type: string
                  description: Customer email
                phone_number:
                  type: string
                  description: Customer phone number
                age:
                  type: integer
                  description: Customer age
                gender:
                  type: string
                  enum:
                  - unspecified
                  - male
                  - female
                  - other
                  description: Customer gender
                company_name:
                  type: string
                  description: Company name
                external_id:
                  type: string
                  description: External ID
                postcode:
                  type: string
                  description: Postcode
                suburb:
                  type: string
                  description: Suburb
                address_line1:
                  type: string
                  description: Address line 1
                address_line2:
                  type: string
                  description: Address line 2
                city:
                  type: string
                  description: City
                state:
                  type: string
                  description: State
                country:
                  type: string
                  description: Country
              required:
              - first_name
              - last_name
              - email
              - phone_number
  "/api/v1/dealerships/{dealership_uuid}/customers/search":
    get:
      summary: Get customers list with search for autocomplete feature
      tags:
      - Customers
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Bearer token
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device identifier
        schema:
          type: string
      - name: dealership_uuid
        in: path
        required: true
        description: Dealership UUID
        schema:
          type: string
      - name: query
        in: query
        required: false
        description: Search term (3-20 characters) for name, email, or phone
        schema:
          type: string
      responses:
        '200':
          description: Empty search results for short query
        '401':
          description: Unauthorized
        '404':
          description: Dealership not found
  "/api/v1/dealerships/{dealership_uuid}/customers/{customer_uuid}":
    get:
      summary: Get customer details
      tags:
      - Customers
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Bearer token
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device identifier
        schema:
          type: string
      - name: dealership_uuid
        in: path
        required: true
        description: Dealership UUID
        schema:
          type: string
      - name: customer_uuid
        in: path
        required: true
        description: Customer UUID
        schema:
          type: string
      responses:
        '200':
          description: Customer retrieved successfully
        '404':
          description: Customer not found
        '401':
          description: Unauthorized
    put:
      summary: Update customer
      tags:
      - Customers
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Bearer token
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device identifier
        schema:
          type: string
      - name: dealership_uuid
        in: path
        required: true
        description: Dealership UUID
        schema:
          type: string
      - name: customer_uuid
        in: path
        required: true
        description: Customer UUID
        schema:
          type: string
      responses:
        '200':
          description: Customer updated successfully
        '404':
          description: Customer not found
        '401':
          description: Unauthorized
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                first_name:
                  type: string
                  description: Customer first name
                last_name:
                  type: string
                  description: Customer last name
                email:
                  type: string
                  description: Customer email
                phone_number:
                  type: string
                  description: Customer phone number
                age:
                  type: integer
                  description: Customer age
                gender:
                  type: string
                  enum:
                  - unspecified
                  - male
                  - female
                  - other
                  description: Customer gender
                company_name:
                  type: string
                  description: Company name
                external_id:
                  type: string
                  description: External ID
                postcode:
                  type: string
                  description: Postcode
                suburb:
                  type: string
                  description: Suburb
                address_line1:
                  type: string
                  description: Address line 1
                address_line2:
                  type: string
                  description: Address line 2
                city:
                  type: string
                  description: City
                state:
                  type: string
                  description: State
                country:
                  type: string
                  description: Country
  "/api/v1/dealerships/{dealership_uuid}/customers/{customer_uuid}/driving-license":
    get:
      summary: Get customer's driving license
      tags:
      - Customers
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Bearer token
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device identifier
        schema:
          type: string
      - name: dealership_uuid
        in: path
        required: true
        description: Dealership UUID
        schema:
          type: string
      - name: customer_uuid
        in: path
        required: true
        description: Customer UUID
        schema:
          type: string
      responses:
        '200':
          description: Driving license retrieved successfully
        '404':
          description: Driving license not found
        '401':
          description: Unauthorized
    post:
      summary: Create or update driving license
      tags:
      - Customers
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Bearer token
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device identifier
        schema:
          type: string
      - name: dealership_uuid
        in: path
        required: true
        description: Dealership UUID
        schema:
          type: string
      - name: customer_uuid
        in: path
        required: true
        description: Customer UUID
        schema:
          type: string
      responses:
        '200':
          description: Driving license created/updated successfully
        '422':
          description: Validation failed
        '401':
          description: Unauthorized
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                licence_number:
                  type: string
                  example: DL123456789
                expiry_date:
                  type: string
                  format: date
                  example: '2025-12-31'
                issue_date:
                  type: string
                  format: date
                  example: '2020-01-01'
                category:
                  type: string
                  example: C
                issuing_country:
                  type: string
                  example: au
                issuing_state:
                  type: string
                  example: NSW
                full_name:
                  type: string
                  example: John Doe
                date_of_birth:
                  type: string
                  format: date
                  example: '1990-01-01'
              required:
              - licence_number
              - expiry_date
    delete:
      summary: Delete driving license
      tags:
      - Customers
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Bearer token
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device identifier
        schema:
          type: string
      - name: dealership_uuid
        in: path
        required: true
        description: Dealership UUID
        schema:
          type: string
      - name: customer_uuid
        in: path
        required: true
        description: Customer UUID
        schema:
          type: string
      responses:
        '200':
          description: Driving license deleted successfully
        '404':
          description: Driving license not found
        '401':
          description: Unauthorized
  "/api/v1/dealerships/{dealership_uuid}/customers/{customer_uuid}/driving-license-image":
    post:
      summary: Upload driving license image
      tags:
      - Customers
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Bearer token
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device identifier
        schema:
          type: string
      - name: dealership_uuid
        in: path
        required: true
        description: Dealership UUID
        schema:
          type: string
      - name: customer_uuid
        in: path
        required: true
        description: Customer UUID
        schema:
          type: string
      responses:
        '200':
          description: Image uploaded successfully
        '404':
          description: Driving license not found
        '422':
          description: Invalid image type or missing file
        '401':
          description: Unauthorized
      requestBody:
        content:
          multipart/form-data:
            schema:
              type: string
        required: true
        description: Type of image
    delete:
      summary: Delete driving license image
      tags:
      - Customers
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Bearer token
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device identifier
        schema:
          type: string
      - name: dealership_uuid
        in: path
        required: true
        description: Dealership UUID
        schema:
          type: string
      - name: customer_uuid
        in: path
        required: true
        description: Customer UUID
        schema:
          type: string
      - name: image_type
        in: query
        enum:
        - front
        - back
        required: true
        description: "Type of image to delete:\n * `front` \n * `back` \n "
        schema:
          type: string
      responses:
        '200':
          description: Image deleted successfully
        '404':
          description: Driving license not found
        '422':
          description: Image not attached or invalid type
        '401':
          description: Unauthorized
  "/api/v1/dealerships/{dealership_uuid}/users":
    get:
      summary: Get dealership users
      tags:
      - Dealership Users
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Bearer token
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device identifier
        schema:
          type: string
      - name: dealership_uuid
        in: path
        required: true
        description: Dealership UUID
        schema:
          type: string
      - name: role_type
        in: query
        required: false
        description: "Filter by user role:\n * `dealership_admin` \n * `sales_person`
          \n * `staff` \n "
        enum:
        - dealership_admin
        - sales_person
        - staff
        schema:
          type: string
      - name: page
        in: query
        required: false
        description: Page number
        schema:
          type: integer
      - name: per_page
        in: query
        required: false
        description: Items per page
        schema:
          type: integer
      responses:
        '200':
          description: Dealership users retrieved successfully
        '422':
          description: Empty results for invalid role filter
        '404':
          description: Dealership not found
        '401':
          description: Unauthorized
  "/api/v1/dealerships":
    get:
      summary: Retrieves dealerships
      tags:
      - Dealerships
      description: Retrieves a list of dealerships associated with the authenticated
        user
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        description: 'Bearer token in the format: Bearer <token>'
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device ID for authentication
        schema:
          type: string
      responses:
        '200':
          description: Dealerships retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 200
                      message:
                        type: string
                        example: Dealerships retrieved successfully
                  data:
                    type: object
                    properties:
                      dealerships:
                        type: array
                        items:
                          type: object
                          properties:
                            uuid:
                              type: string
                              format: uuid
                            name:
                              type: string
                              example: AutoNation Toyota Melbourne
                            setting_date_format:
                              type: string
                              example: dd/mm/yyyy
                            setting_time_zone:
                              type: string
                              example: melbourne
                            setting_distance_unit:
                              type: string
                              example: kilometers
                            status:
                              type: string
                              example: active
                            address_line1:
                              type: string
                              example: 123 Collins Street
                            state:
                              type: string
                              example: Victoria
                            postcode:
                              type: string
                              example: '3000'
                            country:
                              type: string
                              example: au
                            phone:
                              type: string
                              example: "+61412345678"
                            email:
                              type: string
                              example: <EMAIL>
                            website:
                              type: string
                              example: https://autonation.com.au
                            long_name:
                              type:
                              - string
                              - 'null'
                              example: AutoNation Toyota Melbourne Central
                            address_line2:
                              type:
                              - string
                              - 'null'
                              example: Level 5
                            suburb:
                              type:
                              - string
                              - 'null'
                              example: Melbourne
                            external_id:
                              type:
                              - string
                              - 'null'
                            abn:
                              type:
                              - string
                              - 'null'
                              example: '***********'
                            created_at:
                              type: string
                              format: datetime
                            updated_at:
                              type: string
                              format: datetime
                            brand:
                              type: object
                              properties:
                                uuid:
                                  type: string
                                  format: uuid
                                name:
                                  type: string
                                  example: Toyota
        '401':
          description: Missing Device-ID header
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 401
                      message:
                        type: string
                        example: Invalid device
  "/api/v1/dealerships/{dealership_uuid}/drives/{drive_uuid}/trade-plate":
    parameters:
    - name: dealership_uuid
      in: path
      description: Dealership UUID
      required: true
      schema:
        type: string
    - name: drive_uuid
      in: path
      description: Drive UUID
      required: true
      schema:
        type: string
    - name: Authorization
      in: header
      required: true
      description: Bearer token for authentication
      schema:
        type: string
    - name: Device-ID
      in: header
      required: true
      description: Device ID for the request
      schema:
        type: string
    patch:
      summary: Assign trade plate to drive
      tags:
      - Drives
      description: Assigns a trade plate to a drive. The trade plate can only be assigned
        if the vehicle associated with the drive has a blank registration value.
      security:
      - Bearer: []
      parameters: []
      responses:
        '200':
          description: Trade plate assigned successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 200
                      message:
                        type: string
                        example: Trade plate assigned successfully
                  data:
                    type: object
                    properties:
                      drive:
                        uuid:
                          type: string
                          example: 123e4567-e89b-12d3-a456-************
                        drive_type:
                          type: string
                          example: test_drive
                        status:
                          type: string
                          example: scheduled
                        sold_status:
                          type: string
                          example: unsold
                        notes:
                          type: string
                          nullable: true
                        expected_pickup_datetime:
                          type: string
                          format: datetime
                          nullable: true
                        expected_return_datetime:
                          type: string
                          format: datetime
                          nullable: true
                        start_datetime:
                          type: string
                          format: datetime
                          nullable: true
                        end_datetime:
                          type: string
                          format: datetime
                          nullable: true
                        start_odometer_reading:
                          type: integer
                          nullable: true
                        end_odometer_reading:
                          type: integer
                          nullable: true
                        vehicle:
                          type: object
                          properties:
                            uuid:
                              type: string
                            make:
                              type: string
                            model:
                              type: string
                            build_year:
                              type: integer
                            color:
                              type: string
                            rego:
                              type: string
                              nullable: true
                            display_name:
                              type: string
                        customer:
                          type: object
                          nullable: true
                          properties:
                            uuid:
                              type: string
                            first_name:
                              type: string
                            last_name:
                              type: string
                            email:
                              type: string
                            phone_number:
                              type: string
                            full_name:
                              type: string
                        sales_person:
                          type: object
                          properties:
                            uuid:
                              type: string
                            first_name:
                              type: string
                            last_name:
                              type: string
                            email:
                              type: string
                            full_name:
                              type: string
                        sales_person_accompanying:
                          type: object
                          nullable: true
                          properties:
                            uuid:
                              type: string
                            first_name:
                              type: string
                            last_name:
                              type: string
                            email:
                              type: string
                            full_name:
                              type: string
                        trade_plate:
                          type: object
                          nullable: true
                          properties:
                            uuid:
                              type: string
                            number:
                              type: string
                            status:
                              type: string
                            expiry:
                              type: string
                              format: date
                              nullable: true
                        created_at:
                          type: string
                          format: datetime
                        updated_at:
                          type: string
                          format: datetime
        '404':
          description: Trade plate not found or does not belong to this dealership
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 404
                      message:
                        type: string
                        examples:
                          drive_not_found: Drive not found
                          trade_plate_not_found: Trade plate not found or does not
                            belong to this dealership
                        example: Trade plate not found
        '422':
          description: Vehicle must have blank registration
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 422
                      message:
                        type: string
                        example: Trade plate can only be assigned to vehicles with
                          blank registration
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 401
                      message:
                        type: string
                        example: Email or password is incorrect.
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                trade_plate_uuid:
                  type: string
                  description: UUID of the trade plate to assign
                  example: 123e4567-e89b-12d3-a456-************
              required:
              - trade_plate_uuid
  "/api/v1/dealerships/{dealership_uuid}/drives":
    parameters:
    - name: dealership_uuid
      in: path
      description: Dealership UUID
      required: true
      schema:
        type: string
    post:
      summary: Create a new drive
      tags:
      - Drives
      description: Creates a new drive record for a dealership. Only test_drive, loan
        and self_loan are allowed.
      operationId: createDrive
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Bearer token for authentication
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device ID for the request
        schema:
          type: string
      responses:
        '201':
          description: drive created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 201
                      message:
                        type: string
                        example: Drive Created successfully
                    required:
                    - code
                    - message
                  data:
                    type: object
                    properties:
                      drive:
                        type: object
                        properties:
                          uuid:
                            type: string
                            example: 550e8400-e29b-41d4-a716-************
                          drive_type:
                            type: string
                            enum:
                            - test_drive
                            - loan
                            - self_loan
                            example: test_drive
                          status:
                            type: string
                            enum:
                            - draft
                            example: draft
                          sold_status:
                            type: string
                            enum:
                            - unsold
                            - sold
                            example: unsold
                          notes:
                            type: string
                            nullable: true
                          expected_pickup_datetime:
                            type: string
                            format: date-time
                            nullable: true
                          expected_return_datetime:
                            type: string
                            format: date-time
                            nullable: true
                          start_datetime:
                            type: string
                            format: date-time
                            nullable: true
                          end_datetime:
                            type: string
                            format: date-time
                            nullable: true
                          start_odometer_reading:
                            type: integer
                            nullable: true
                          end_odometer_reading:
                            type: integer
                            nullable: true
                          vehicle:
                            type: object
                            properties:
                              uuid:
                                type: string
                                example: 550e8400-e29b-41d4-a716-************
                              make:
                                type: string
                                example: Toyota
                              model:
                                type: string
                                example: Camry
                              build_year:
                                type: integer
                                example: 2023
                              color:
                                type: string
                                example: White
                              rego:
                                type: string
                                example: ABC123
                              display_name:
                                type: string
                                example: 2023 Toyota Camry
                          customer:
                            type: object
                            nullable: true
                            properties:
                              uuid:
                                type: string
                                example: 550e8400-e29b-41d4-a716-************
                              first_name:
                                type: string
                                example: John
                              last_name:
                                type: string
                                example: Doe
                              email:
                                type: string
                                example: <EMAIL>
                              phone_number:
                                type: string
                                example: "+61400000000"
                              full_name:
                                type: string
                                example: John Doe
                          sales_person:
                            type: object
                            properties:
                              uuid:
                                type: string
                                example: 550e8400-e29b-41d4-a716-************
                              first_name:
                                type: string
                                example: Jane
                              last_name:
                                type: string
                                example: Smith
                              email:
                                type: string
                                example: <EMAIL>
                              full_name:
                                type: string
                                example: Jane Smith
                          sales_person_accompanying:
                            type: object
                            nullable: true
                            properties:
                              uuid:
                                type: string
                                example: 550e8400-e29b-41d4-a716-************
                              first_name:
                                type: string
                                example: Bob
                              last_name:
                                type: string
                                example: Johnson
                              email:
                                type: string
                                example: <EMAIL>
                              full_name:
                                type: string
                                example: Bob Johnson
                          trade_plate:
                            type: object
                            nullable: true
                            properties:
                              uuid:
                                type: string
                                example: 550e8400-e29b-41d4-a716-************
                              number:
                                type: string
                                example: TP001
                              status:
                                type: string
                                enum:
                                - active
                                - inactive
                                example: active
                              expiry:
                                type: string
                                format: date
                                example: '2024-12-31'
                          created_at:
                            type: string
                            format: date-time
                            example: '2023-07-01T09:00:00Z'
                          updated_at:
                            type: string
                            format: date-time
                            example: '2023-07-01T12:00:00Z'
                    required:
                    - drive
                required:
                - status
                - data
        '422':
          description: invalid drive type
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 422
                      message:
                        type: string
                        example: Invalid Drive Type.
                    required:
                    - code
                    - message
                required:
                - status
        '404':
          description: dealership not found
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 404
                      message:
                        type: string
                        example: Dealership not found or you don't have access to
                          it
                    required:
                    - code
                    - message
                required:
                - status
        '401':
          description: unauthorized
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 401
                      message:
                        type: string
                        example: Missing or invalid authorization token
                    required:
                    - code
                    - message
                required:
                - status
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required:
              - vehicle_uuid
              - drive_type
              properties:
                vehicle_uuid:
                  type: string
                  description: UUID of the vehicle for the drive
                drive_type:
                  type: string
                  enum:
                  - test_drive
                  - loan
                  - self_loan
                  description: Type of drive
                sales_person_uuid:
                  type: string
                  description: UUID of the sales person (optional)
    get:
      summary: List drives for a dealership
      tags:
      - Drives
      description: Retrieves all drives for a specific dealership with pagination
        and filtering options. Results are ordered by start_datetime descending.
      operationId: getDrives
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Bearer token for authentication
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device ID for the request
        schema:
          type: string
      - name: page
        in: query
        required: false
        description: 'Page number (default: 1)'
        schema:
          type: integer
      - name: per_page
        in: query
        required: false
        description: 'Items per page (default: 20, max: 100)'
        schema:
          type: integer
      - name: vehicle_uuid
        in: query
        required: false
        description: Filter by vehicle UUID
        schema:
          type: string
      - name: customer_uuid
        in: query
        required: false
        description: Filter by customer UUID
        schema:
          type: string
      - name: drive_type
        in: query
        required: false
        description: Filter by drive type
        schema:
          type: string
          enum:
          - test_drive
          - enquiry
          - loan
          - appraisal
          - loan_booking
          - test_drive_booking
          - self_loan
      - name: sales_person_uuid
        in: query
        required: false
        description: Filter by sales person UUID
        schema:
          type: string
      - name: status
        in: query
        required: false
        description: Filter by drive status
        schema:
          type: string
          enum:
          - scheduled
          - in_progress
          - completed
          - cancelled
          - draft
          - deleted
      - name: sold_status
        in: query
        required: false
        description: Filter by sold status
        schema:
          type: string
          enum:
          - unsold
          - sold
      - name: trade_plate_uuid
        in: query
        required: false
        description: Filter by trade plate UUID
        schema:
          type: string
      - name: start_date_from
        in: query
        format: date
        required: false
        description: Filter by start date from (YYYY-MM-DD)
        schema:
          type: string
      - name: start_date_to
        in: query
        format: date
        required: false
        description: Filter by start date to (YYYY-MM-DD)
        schema:
          type: string
      responses:
        '200':
          description: empty results for invalid filter
          headers:
            X-Current-Page:
              schema:
                type: string
              description: Current page number
            X-Per-Page:
              schema:
                type: string
              description: Items per page
            X-Total-Count:
              schema:
                type: string
              description: Total number of items
            X-Total-Pages:
              schema:
                type: string
              description: Total number of pages
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 200
                      message:
                        type: string
                        example: Drives retrieved successfully
                    required:
                    - code
                    - message
                  data:
                    type: object
                    properties:
                      drives:
                        type: array
                        items:
                          type: object
                          properties:
                            uuid:
                              type: string
                              example: 550e8400-e29b-41d4-a716-************
                            drive_type:
                              type: string
                              enum:
                              - test_drive
                              - enquiry
                              - loan
                              - appraisal
                              - loan_booking
                              - test_drive_booking
                              - self_loan
                              example: test_drive
                            status:
                              type: string
                              enum:
                              - scheduled
                              - in_progress
                              - completed
                              - cancelled
                              - draft
                              - deleted
                              example: completed
                            sold_status:
                              type: string
                              enum:
                              - unsold
                              - sold
                              example: sold
                            notes:
                              type: string
                              example: Customer interested in purchasing
                              nullable: true
                            expected_pickup_datetime:
                              type: string
                              format: date-time
                              example: '2023-07-01T10:00:00Z'
                              nullable: true
                            expected_return_datetime:
                              type: string
                              format: date-time
                              example: '2023-07-01T12:00:00Z'
                              nullable: true
                            start_datetime:
                              type: string
                              format: date-time
                              example: '2023-07-01T10:15:00Z'
                              nullable: true
                            end_datetime:
                              type: string
                              format: date-time
                              example: '2023-07-01T11:45:00Z'
                              nullable: true
                            start_odometer_reading:
                              type: integer
                              example: 15000
                              nullable: true
                            end_odometer_reading:
                              type: integer
                              example: 15025
                              nullable: true
                            vehicle:
                              type: object
                              properties:
                                uuid:
                                  type: string
                                  example: 550e8400-e29b-41d4-a716-************
                                make:
                                  type: string
                                  example: Toyota
                                model:
                                  type: string
                                  example: Camry
                                build_year:
                                  type: integer
                                  example: 2023
                                color:
                                  type: string
                                  example: Blue
                                rego:
                                  type: string
                                  example: ABC123
                                display_name:
                                  type: string
                                  example: 2023 Toyota Camry
                            customer:
                              type: object
                              nullable: true
                              properties:
                                uuid:
                                  type: string
                                  example: 550e8400-e29b-41d4-a716-************
                                first_name:
                                  type: string
                                  example: John
                                last_name:
                                  type: string
                                  example: Doe
                                full_name:
                                  type: string
                                  example: John Doe
                                email:
                                  type: string
                                  example: <EMAIL>
                                phone_number:
                                  type: string
                                  example: "+61412345678"
                            sales_person:
                              type: object
                              properties:
                                uuid:
                                  type: string
                                  example: 550e8400-e29b-41d4-a716-************
                                first_name:
                                  type: string
                                  example: Jane
                                last_name:
                                  type: string
                                  example: Smith
                                full_name:
                                  type: string
                                  example: Jane Smith
                                email:
                                  type: string
                                  example: <EMAIL>
                            sales_person_accompanying:
                              type: object
                              nullable: true
                              properties:
                                uuid:
                                  type: string
                                  example: 550e8400-e29b-41d4-a716-************
                                first_name:
                                  type: string
                                  example: Bob
                                last_name:
                                  type: string
                                  example: Wilson
                                full_name:
                                  type: string
                                  example: Bob Wilson
                                email:
                                  type: string
                                  example: <EMAIL>
                            trade_plate:
                              type: object
                              nullable: true
                              properties:
                                uuid:
                                  type: string
                                  example: 550e8400-e29b-41d4-a716-************
                                number:
                                  type: string
                                  example: TP001
                                expiry:
                                  type: string
                                  format: date
                                  example: '2024-12-31'
                                status:
                                  type: string
                                  enum:
                                  - active
                                  - inactive
                                  example: active
                            driver_license:
                              type: object
                              nullable: true
                              properties:
                                uuid:
                                  type: string
                                  example: 550e8400-e29b-41d4-a716-************
                                licence_number:
                                  type: string
                                  example: DL123456789
                                full_name:
                                  type: string
                                  example: John Doe
                                expiry_date:
                                  type: string
                                  format: date
                                  example: '2025-06-30'
                            created_at:
                              type: string
                              format: date-time
                              example: '2023-07-01T09:00:00Z'
                            updated_at:
                              type: string
                              format: date-time
                              example: '2023-07-01T12:00:00Z'
                    required:
                    - drives
                required:
                - status
                - data
        '401':
          description: unauthorized
        '404':
          description: dealership not found
  "/api/v1/dealerships/{dealership_uuid}/drives/{uuid}/damage-report":
    post:
      summary: Create damage report for drive
      tags:
      - Drives
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Bearer token
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device identifier
        schema:
          type: string
      - name: dealership_uuid
        in: path
        required: true
        description: Dealership UUID
        schema:
          type: string
      - name: uuid
        in: path
        required: true
        description: Drive UUID
        schema:
          type: string
      responses:
        '201':
          description: Damage report created without media files
        '422':
          description: Validation error
        '404':
          description: Drive not found
      requestBody:
        content:
          multipart/form-data:
            schema:
              type: string
        required: true
        description: Type of damage report
  "/api/v1/dealerships/{dealership_uuid}/drives/{uuid}":
    parameters:
    - name: dealership_uuid
      in: path
      description: Dealership UUID
      required: true
      schema:
        type: string
    - name: uuid
      in: path
      description: Drive UUID
      required: true
      schema:
        type: string
    get:
      summary: Get drive by ID
      tags:
      - Drives
      description: Retrieves a specific drive by its UUID for a dealership
      operationId: getDriveById
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Bearer token for authentication
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device ID for the request
        schema:
          type: string
      responses:
        '200':
          description: successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 200
                      message:
                        type: string
                        example: Drive retrieved successfully
                    required:
                    - code
                    - message
                  data:
                    type: object
                    properties:
                      drive:
                        type: object
                        properties:
                          uuid:
                            type: string
                            example: 550e8400-e29b-41d4-a716-************
                          drive_type:
                            type: string
                            enum:
                            - test_drive
                            - enquiry
                            - loan
                            - appraisal
                            - loan_booking
                            - test_drive_booking
                            - self_loan
                            example: test_drive
                          status:
                            type: string
                            enum:
                            - scheduled
                            - in_progress
                            - completed
                            - cancelled
                            - draft
                            - deleted
                            example: completed
                          sold_status:
                            type: string
                            enum:
                            - unsold
                            - sold
                            example: sold
                          notes:
                            type: string
                            example: Customer interested in purchasing
                            nullable: true
                          expected_pickup_datetime:
                            type: string
                            format: date-time
                            example: '2023-07-01T10:00:00Z'
                            nullable: true
                          expected_return_datetime:
                            type: string
                            format: date-time
                            example: '2023-07-01T12:00:00Z'
                            nullable: true
                          start_datetime:
                            type: string
                            format: date-time
                            example: '2023-07-01T10:15:00Z'
                            nullable: true
                          end_datetime:
                            type: string
                            format: date-time
                            example: '2023-07-01T11:45:00Z'
                            nullable: true
                          start_odometer_reading:
                            type: integer
                            example: 15000
                            nullable: true
                          end_odometer_reading:
                            type: integer
                            example: 15025
                            nullable: true
                          vehicle:
                            type: object
                            properties:
                              uuid:
                                type: string
                                example: 550e8400-e29b-41d4-a716-************
                              make:
                                type: string
                                example: Toyota
                              model:
                                type: string
                                example: Camry
                              build_year:
                                type: integer
                                example: 2023
                              color:
                                type: string
                                example: Blue
                              rego:
                                type: string
                                example: ABC123
                              display_name:
                                type: string
                                example: 2023 Toyota Camry
                          customer:
                            type: object
                            nullable: true
                            properties:
                              uuid:
                                type: string
                                example: 550e8400-e29b-41d4-a716-************
                              first_name:
                                type: string
                                example: John
                              last_name:
                                type: string
                                example: Doe
                              full_name:
                                type: string
                                example: John Doe
                              email:
                                type: string
                                example: <EMAIL>
                              phone_number:
                                type: string
                                example: "+61412345678"
                          sales_person:
                            type: object
                            properties:
                              uuid:
                                type: string
                                example: 550e8400-e29b-41d4-a716-************
                              first_name:
                                type: string
                                example: Jane
                              last_name:
                                type: string
                                example: Smith
                              full_name:
                                type: string
                                example: Jane Smith
                              email:
                                type: string
                                example: <EMAIL>
                          sales_person_accompanying:
                            type: object
                            nullable: true
                            properties:
                              uuid:
                                type: string
                                example: 550e8400-e29b-41d4-a716-************
                              first_name:
                                type: string
                                example: Bob
                              last_name:
                                type: string
                                example: Wilson
                              full_name:
                                type: string
                                example: Bob Wilson
                              email:
                                type: string
                                example: <EMAIL>
                          trade_plate:
                            type: object
                            nullable: true
                            properties:
                              uuid:
                                type: string
                                example: 550e8400-e29b-41d4-a716-************
                              number:
                                type: string
                                example: TP001
                              expiry:
                                type: string
                                format: date
                                example: '2024-12-31'
                              status:
                                type: string
                                enum:
                                - active
                                - inactive
                                example: active
                          driver_license:
                            type: object
                            nullable: true
                            properties:
                              uuid:
                                type: string
                                example: 550e8400-e29b-41d4-a716-************
                              licence_number:
                                type: string
                                example: DL123456789
                              full_name:
                                type: string
                                example: John Doe
                              expiry_date:
                                type: string
                                format: date
                                example: '2025-06-30'
                          created_at:
                            type: string
                            format: date-time
                            example: '2023-07-01T09:00:00Z'
                          updated_at:
                            type: string
                            format: date-time
                            example: '2023-07-01T12:00:00Z'
                    required:
                    - drive
                required:
                - status
                - data
        '401':
          description: unauthorized
        '404':
          description: drive not found
  "/api/v1/dealerships/{dealership_uuid}/drives/{uuid}/customer":
    put:
      summary: Update customer for drive
      tags:
      - Drives
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Bearer token
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device identifier
        schema:
          type: string
      - name: dealership_uuid
        in: path
        required: true
        description: Dealership UUID
        schema:
          type: string
      - name: uuid
        in: path
        required: true
        description: Drive UUID
        schema:
          type: string
      responses:
        '200':
          description: Customer updated successfully with new customer and driver
            license
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      message:
                        type: string
                        example: Customer updated successfully
                  data:
                    type: object
                    properties:
                      drive:
                        type: object
                        properties:
                          uuid:
                            type: string
                          customer:
                            type: object
                            properties:
                              uuid:
                                type: string
                              first_name:
                                type: string
                              last_name:
                                type: string
                              email:
                                type: string
                              phone_number:
                                type: string
                              full_name:
                                type: string
                              driver_license:
                                type: object
                                nullable: true
                                properties:
                                  uuid:
                                    type: string
                                  licence_number:
                                    type: string
                                  expiry_date:
                                    type: string
                                    format: date
                                  issuing_state:
                                    type: string
                                  issuing_country:
                                    type: string
                                  full_name:
                                    type: string
                                  date_of_birth:
                                    type: string
                                    format: date
                                  front_image_url:
                                    type: string
                                    nullable: true
                                    description: URL of front image
                                  back_image_url:
                                    type: string
                                    nullable: true
                                    description: URL of back image
        '422':
          description: Validation error
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      message:
                        type: string
        '404':
          description: Customer not found
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      message:
                        type: string
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      message:
                        type: string
      requestBody:
        content:
          application/json:
            schema:
              oneOf:
              - type: object
                properties:
                  customer_uuid:
                    type: string
                    description: UUID of an existing customer
                    example: 123e4567-e89b-12d3-a456-************
                required:
                - customer_uuid
              - type: object
                properties:
                  customer_info:
                    type: object
                    properties:
                      first_name:
                        type: string
                        example: John
                      last_name:
                        type: string
                        example: Doe
                      email:
                        type: string
                        example: <EMAIL>
                      phone_number:
                        type: string
                        example: "+61412345678"
                      age:
                        type: integer
                        example: 30
                      gender:
                        type: string
                        enum:
                        - unspecified
                        - male
                        - female
                        - other
                        example: male
                      address_line1:
                        type: string
                        example: 123 Main St
                      address_line2:
                        type: string
                        example: Apt 4B
                      suburb:
                        type: string
                        example: Richmond
                      city:
                        type: string
                        example: Melbourne
                      state:
                        type: string
                        example: VIC
                      country:
                        type: string
                        example: Australia
                      postcode:
                        type: string
                        example: '3121'
                      company_name:
                        type: string
                        example: ACME Corp
                      driver_license:
                        type: object
                        properties:
                          licence_number:
                            type: string
                            example: '12345678'
                          expiry_date:
                            type: string
                            format: date
                            example: '2025-12-31'
                          issuing_state:
                            type: string
                            example: VIC
                          issuing_country:
                            type: string
                            example: AU
                          full_name:
                            type: string
                            example: John Doe
                          date_of_birth:
                            type: string
                            format: date
                            example: '1990-01-01'
                          front_image:
                            type: string
                            format: binary
                            description: Front image of driver license
                          back_image:
                            type: string
                            format: binary
                            description: Back image of driver license
                    required:
                    - first_name
                    - last_name
                    - email
                    - phone_number
                required:
                - customer_info
  "/api/v1/dealerships/{dealership_uuid}/drives/{uuid}/return-time":
    put:
      summary: Update drive return time
      tags:
      - Drives
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Bearer token
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device identifier
        schema:
          type: string
      - name: dealership_uuid
        in: path
        required: true
        description: Dealership UUID
        schema:
          type: string
      - name: uuid
        in: path
        required: true
        description: Drive UUID
        schema:
          type: string
      responses:
        '200':
          description: Return time updated successfully
        '422':
          description: Invalid return time update
        '404':
          description: Drive not found
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required:
              - drive
              properties:
                drive:
                  type: object
                  required:
                  - expected_return_datetime
                  properties:
                    expected_return_datetime:
                      type: string
                      format: date-time
        required: true
  "/api/v1/dealerships/{dealership_uuid}/drives/{uuid}/odometer":
    put:
      summary: Update drive odometer reading
      tags:
      - Drives
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Bearer token
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device identifier
        schema:
          type: string
      - name: dealership_uuid
        in: path
        required: true
        description: Dealership UUID
        schema:
          type: string
      - name: uuid
        in: path
        required: true
        description: Drive UUID
        schema:
          type: string
      responses:
        '200':
          description: Odometer reading updated successfully
        '422':
          description: Invalid odometer update
        '404':
          description: Drive not found
        '401':
          description: Unauthorized
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required:
              - drive
              properties:
                drive:
                  type: object
                  properties:
                    start_odometer_reading:
                      type: integer
                    end_odometer_reading:
                      type: integer
        required: true
  "/api/v1/dealerships/{dealership_uuid}/drives/{uuid}/reassign":
    put:
      summary: Reassign drive to different users
      tags:
      - Drives
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Bearer token
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device identifier
        schema:
          type: string
      - name: dealership_uuid
        in: path
        required: true
        description: Dealership UUID
        schema:
          type: string
      - name: uuid
        in: path
        required: true
        description: Drive UUID
        schema:
          type: string
      responses:
        '200':
          description: Drive reassigned with only accompanying person
        '422':
          description: Validation error
        '404':
          description: Drive not found
        '401':
          description: Unauthorized
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                sales_person_uuid:
                  type: string
                  description: UUID of the new sales person to assign to the drive
                  example: 123e4567-e89b-12d3-a456-************
                sales_person_accompanying_uuid:
                  type: string
                  description: UUID of the new accompanying sales person to assign
                    to the drive
                  example: 123e4567-e89b-12d3-a456-************
        required: true
  "/api/v1/dealerships/{dealership_uuid}/trade-plates":
    parameters:
    - name: dealership_uuid
      in: path
      description: Dealership UUID
      required: true
      schema:
        type: string
    get:
      summary: List trade plates for a dealership
      tags:
      - Trade Plates
      description: Retrieves all trade plates for a specific dealership. Returns flags
        indicating if each trade plate is expired or currently in use by an in-progress
        drive.
      operationId: getTradePlates
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Bearer token for authentication
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device ID for the request
        schema:
          type: string
      responses:
        '200':
          description: successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 200
                      message:
                        type: string
                        example: Trade plates retrieved successfully
                    required:
                    - code
                    - message
                  data:
                    type: object
                    properties:
                      trade_plates:
                        type: array
                        items:
                          type: object
                          properties:
                            uuid:
                              type: string
                              example: 550e8400-e29b-41d4-a716-************
                            number:
                              type: string
                              example: TP001
                            expiry:
                              type: string
                              format: date
                              example: '2024-12-31'
                              nullable: true
                            status:
                              type: string
                              enum:
                              - active
                              - inactive
                              example: active
                            expired:
                              type: boolean
                              example: false
                              description: Flag indicating if the trade plate is expired
                            in_use:
                              type: boolean
                              example: false
                              description: Flag indicating if the trade plate is currently
                                being used by an in-progress drive
                            created_at:
                              type: string
                              format: date-time
                              example: '2023-01-01T12:00:00Z'
                            updated_at:
                              type: string
                              format: date-time
                              example: '2023-01-01T12:00:00Z'
                          required:
                          - uuid
                          - number
                          - status
                          - expired
                          - in_use
                          - created_at
                          - updated_at
                    required:
                    - trade_plates
                required:
                - status
                - data
        '401':
          description: unauthorized
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 401
                      message:
                        type: string
                        example: Missing authorization token
                    required:
                    - code
                    - message
                required:
                - status
        '404':
          description: not found
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 404
                      message:
                        type: string
                        example: Dealership not found or you don't have access to
                          it
                    required:
                    - code
                    - message
                required:
                - status
  "/api/v1/dealerships/{dealership_uuid}/vehicles":
    post:
      summary: Create a new vehicle with photo uploads
      tags:
      - Vehicles
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        schema:
          type: string
      - name: dealership_uuid
        in: path
        required: true
        schema:
          type: string
      responses:
        '201':
          description: Vehicle created successfully with photos
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 201
                      message:
                        type: string
                        example: Vehicle created successfully
                  data:
                    type: object
                    properties:
                      vehicle:
                        type: object
                        properties:
                          uuid:
                            type: string
                            format: uuid
                          make:
                            type: string
                            example: Toyota
                          model:
                            type: string
                            example: Camry
                          build_year:
                            type: integer
                            example: 2023
                          color:
                            type: string
                            example: Blue
                          vin:
                            type: string
                            example: 1HGBH41JXMN109186
                          stock_number:
                            type: string
                            example: TOY001
                          rego:
                            type: string
                            example: ABC123
                          status:
                            type: string
                            example: available
                          vehicle_type:
                            type: string
                            example: new_vehicle
                          photos_count:
                            type: integer
                            example: 2
                          photo_urls:
                            type: array
                            items:
                              type: string
                              format: uri
                            example:
                            - https://example.com/photo1.jpg
                            - https://example.com/photo2.jpg
                          brand:
                            type: object
                            nullable: true
                            properties:
                              uuid:
                                type: string
                                format: uuid
                              name:
                                type: string
                                example: Toyota
        '422':
          description: Validation error
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 422
                      message:
                        type: string
                        example: Photos exceeds maximum limit of 5 files
        '401':
          description: Unauthorized
        '404':
          description: Dealership not found
        '400':
          description: Bad request
      requestBody:
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                brand_uuid:
                  type: string
                  description: Brand UUID
                make:
                  type: string
                  description: Vehicle make
                model:
                  type: string
                  description: Vehicle model
                build_year:
                  type: integer
                  description: Vehicle build year
                color:
                  type: string
                  description: Vehicle color
                vin:
                  type: string
                  description: Vehicle VIN
                stock_number:
                  type: string
                  description: Stock number
                rego:
                  type: string
                  description: Registration number
                status:
                  type: string
                  enum:
                  - available
                  - in_use
                  - out_of_service
                  - sold
                  - enquiry
                vehicle_type:
                  type: string
                  enum:
                  - new_vehicle
                  - demo
                  - old
                rego_expiry:
                  type:
                  - string
                  - 'null'
                  format: date
                  description: Registration expiry date
                is_trade_plate_used:
                  type: boolean
                  description: Flag indicating if trade plate is used
                available_for_drive:
                  type: boolean
                  description: Flag indicating if vehicle is available for drive
                last_known_odometer_km:
                  type:
                  - integer
                  - 'null'
                  description: Last known odometer reading in kilometers
                last_known_fuel_gauge_level:
                  type:
                  - integer
                  - 'null'
                  description: Last known fuel gauge level
                photos:
                  type: array
                  items:
                    type: string
                    format: binary
                    description: Vehicle photo (PNG/JPEG only, max 5MB, max 5 files)
              required:
              - make
              - model
              - build_year
    parameters:
    - name: dealership_uuid
      in: path
      description: Dealership UUID
      required: true
      schema:
        type: string
    get:
      summary: Retrieves dealership vehicles
      tags:
      - Vehicles
      description: Retrieves a paginated list of vehicles for a specific dealership
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        description: 'Bearer token in the format: Bearer <token>'
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device ID for authentication
        schema:
          type: string
      - name: page
        in: query
        required: false
        description: 'Page number for pagination (default: 1)'
        schema:
          type: integer
      - name: per_page
        in: query
        required: false
        description: 'Number of items per page (default: 20, max: 100)'
        schema:
          type: integer
      - name: vehicle_type
        in: query
        required: false
        description: "Filter by vehicle type:\n * `new_vehicle` \n * `demo` \n * `old`
          \n "
        enum:
        - new_vehicle
        - demo
        - old
        schema:
          type: string
      responses:
        '200':
          description: Vehicles filtered by type
          headers:
            X-Current-Page:
              schema:
                type: string
              description: Current page number
            X-Per-Page:
              schema:
                type: string
              description: Items per page
            X-Total-Count:
              schema:
                type: string
              description: Total number of items
            X-Total-Pages:
              schema:
                type: string
              description: Total number of pages
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 200
                      message:
                        type: string
                        example: Dealership vehicles retrieved successfully
                  data:
                    type: object
                    properties:
                      vehicles:
                        type: array
                        items:
                          type: object
                          properties:
                            uuid:
                              type: string
                              format: uuid
                            make:
                              type: string
                              example: Toyota
                            model:
                              type: string
                              example: Camry
                            build_year:
                              type: integer
                              example: 2023
                            rego:
                              type: string
                              example: ABC123
                            vin:
                              type: string
                              example: '***********234567'
                            stock_number:
                              type: string
                              example: T001
                            color:
                              type: string
                              example: Red
                            vehicle_type:
                              type: string
                              example: new_vehicle
                            status:
                              type: string
                              example: available
                            last_known_odometer_km:
                              type: integer
                              example: 10000
                            last_known_fuel_gauge_level:
                              type: integer
                              example: 75
                            display_name:
                              type: string
                              example: 2023 Toyota Camry
                            last_system_inspection_timestamp:
                              type: string
                              format: datetime
                            rego_expiry:
                              type: string
                              format: date
                            is_trade_plate_used:
                              type: boolean
                              example: false
                            available_for_drive:
                              type: boolean
                              example: true
                            created_at:
                              type: string
                              format: datetime
                            updated_at:
                              type: string
                              format: datetime
        '422':
          description: Invalid vehicle type filter
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 422
                      message:
                        type: string
                        example: 'Invalid vehicle type filter. Valid filters: new_vehicle,
                          demo, old'
        '401':
          description: Missing Device-ID header
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 401
                      message:
                        type: string
                        example: Invalid device
        '404':
          description: Dealership not found
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 404
                      message:
                        type: string
                        example: Dealership not found
  "/api/v1/devices":
    get:
      summary: List user devices
      tags:
      - User Devices
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        schema:
          type: string
      responses:
        '200':
          description: Devices retrieved successfully
        '401':
          description: Token expired
    delete:
      summary: Logout all devices
      tags:
      - User Devices
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        schema:
          type: string
      responses:
        '200':
          description: All devices logged out successfully
        '401':
          description: Missing device ID
    patch:
      summary: Update device details
      tags:
      - User Devices
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Bearer token
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device identifier
        schema:
          type: string
      responses:
        '200':
          description: Device details updated successfully
        '401':
          description: Unauthorized
        '422':
          description: Validation failed
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                device_os:
                  type: string
                  example: ios
                device_os_version:
                  type: string
                  example: 1.2.3
                app_version:
                  type: string
                  example: 1.0.0
                app_build_number:
                  type: string
                  example: 1.0.0
                fcm_token:
                  type: string
                  example: fcm-token-123
                device_name:
                  type: string
                  example: IPhone 15
  "/api/v1/auth/logout":
    delete:
      summary: Logout current device
      tags:
      - Authentication
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        schema:
          type: string
      responses:
        '200':
          description: Device logged out successfully
        '401':
          description: Invalid device
  "/api/v1/devices/{device_id}":
    delete:
      summary: Logout a specific device
      tags:
      - User Devices
      security:
      - Bearer: []
      parameters:
      - name: device_id
        in: path
        required: true
        schema:
          type: string
      - name: Authorization
        in: header
        required: true
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        schema:
          type: string
      responses:
        '200':
          description: Device logged out successfully
        '404':
          description: Device not found
  "/api/v1/auth/login":
    post:
      summary: Login
      tags:
      - Authentication
      parameters: []
      responses:
        '200':
          description: Login successful, OTP sent
        '401':
          description: Missing credentials
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                user:
                  type: object
                  properties:
                    email:
                      type: string
                      example: <EMAIL>
                    password:
                      type: string
                      example: Drive@2025
                  required:
                  - email
                  - password
              required:
              - user
        required: true
  "/api/v1/auth/change-password":
    put:
      summary: Change Password
      tags:
      - Authentication
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        schema:
          type: string
      responses:
        '200':
          description: password changed
        '422':
          description: Weak new password
        '401':
          description: Unauthorized
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required:
              - current_password
              - new_password
              properties:
                current_password:
                  type: string
                new_password:
                  type: string
  "/api/v1/profile/photo":
    delete:
      summary: Delete profile photo
      tags:
      - Users
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        schema:
          type: string
      responses:
        '200':
          description: Profile photo deleted successfully
        '422':
          description: No photo attached
    put:
      summary: Update profile photo
      tags:
      - Users
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        example: Bearer <token>
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        example: device-123
        schema:
          type: string
      responses:
        '200':
          description: Photo updated successfully
        '422':
          description: Unsupported file type
        '401':
          description: Token has expired
      requestBody:
        content:
          multipart/form-data:
            schema:
              type: file
        required: true
        description: Image file (JPEG/PNG)
  "/api/v1/users/me/documents/driving-license":
    get:
      summary: Get user's driving license
      tags:
      - User Documents
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Bearer token
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device identifier
        schema:
          type: string
      responses:
        '200':
          description: Driving license retrieved successfully
        '404':
          description: Driving license not found
        '401':
          description: Unauthorized
    post:
      summary: Create or update driving license
      tags:
      - User Documents
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Bearer token
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device identifier
        schema:
          type: string
      responses:
        '200':
          description: Driving license created/updated successfully
        '422':
          description: Validation failed
        '401':
          description: Unauthorized
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                licence_number:
                  type: string
                  example: DL123456789
                expiry_date:
                  type: string
                  format: date
                  example: '2025-12-31'
                issue_date:
                  type: string
                  format: date
                  example: '2020-01-01'
                category:
                  type: string
                  example: C
                issuing_country:
                  type: string
                  example: au
                issuing_state:
                  type: string
                  example: NSW
                full_name:
                  type: string
                  example: John Doe
                date_of_birth:
                  type: string
                  format: date
                  example: '1990-01-01'
              required:
              - licence_number
              - expiry_date
    delete:
      summary: Delete driving license
      tags:
      - User Documents
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Bearer token
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device identifier
        schema:
          type: string
      responses:
        '200':
          description: Driving license deleted successfully
        '404':
          description: Driving license not found
        '401':
          description: Unauthorized
  "/api/v1/users/me/documents/driving-license-image":
    post:
      summary: Upload driving license image
      tags:
      - User Documents
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Bearer token
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device identifier
        schema:
          type: string
      responses:
        '200':
          description: Image uploaded successfully
        '404':
          description: Driving license not found
        '422':
          description: Invalid image type or missing file
        '401':
          description: Unauthorized
      requestBody:
        content:
          multipart/form-data:
            schema:
              type: string
        required: true
        description: Type of image
    delete:
      summary: Delete driving license image
      tags:
      - User Documents
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Bearer token
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device identifier
        schema:
          type: string
      - name: image_type
        in: query
        enum:
        - front
        - back
        required: true
        description: "Type of image to delete:\n * `front` \n * `back` \n "
        schema:
          type: string
      responses:
        '200':
          description: Image deleted successfully
        '404':
          description: Driving license not found
        '422':
          description: Image not attached or invalid type
        '401':
          description: Unauthorized
  "/api/v1/auth/forgot-password":
    post:
      summary: Forgot Password
      tags:
      - Authentication
      parameters: []
      responses:
        '200':
          description: reset code sent
        '404':
          description: user not found
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required:
              - email
              properties:
                email:
                  type: string
                  example: <EMAIL>
  "/api/v1/profile":
    get:
      summary: Get profile
      tags:
      - Users
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        example: Bearer <token>
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        example: device-123
        schema:
          type: string
      responses:
        '200':
          description: Profile retrieved successfully
        '401':
          description: Invalid token
    patch:
      summary: Update user profile
      tags:
      - Users
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Bearer token
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device identifier
        schema:
          type: string
      responses:
        '200':
          description: Profile updated successfully
        '401':
          description: Unauthorized
        '422':
          description: Validation failed
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                first_name:
                  type: string
                  example: Jane
                last_name:
                  type: string
                  example: Smith
                preferred_2fa:
                  type: string
                  enum:
                  - email
                  - sms
                  example: email
                job_title:
                  type: string
                  example: Sales Manager
                preferred_language:
                  type: string
                  example: spanish
                time_zone:
                  type: string
                  example: brisbane
                onboarding_completed:
                  type: boolean
                  example: true
  "/api/v1/auth/refresh":
    post:
      summary: Refresh Access Token
      tags:
      - Authentication
      parameters: []
      responses:
        '200':
          description: Token refreshed successfully
        '401':
          description: Invalid refresh token
        '422':
          description: Missing parameters
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required:
              - refresh_token
              - device_id
              properties:
                refresh_token:
                  type: string
                  example: valid.refresh.token
                device_id:
                  type: string
                  example: device123
  "/api/v1/auth/resend-otp":
    post:
      summary: Resend OTP
      tags:
      - Authentication
      parameters: []
      responses:
        '200':
          description: OTP resent successfully
        '401':
          description: Invalid token
        '422':
          description: Missing method param
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required:
              - temporary_token
              - method
              properties:
                temporary_token:
                  type: string
                  example: valid.temp.token
                method:
                  type: string
                  enum:
                  - email
                  - sms
                  example: email
  "/api/v1/auth/reset-password":
    post:
      summary: Reset Password
      tags:
      - Authentication
      parameters: []
      responses:
        '200':
          description: password reset
        '422':
          description: missing or invalid inputs
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required:
              - temporary_token
              - new_password
              properties:
                temporary_token:
                  type: string
                  example: valid.token
                new_password:
                  type: string
                  example: NewPass@123
  "/api/v1/auth/setup-2fa":
    post:
      summary: Setup 2FA with authenticator app
      tags:
      - Authentication
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Bearer token
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device identifier
        schema:
          type: string
      responses:
        '200':
          description: 2FA setup initiated successfully
        '401':
          description: Unauthorized
  "/api/v1/auth/verify-2fa-setup":
    post:
      summary: Verify 2FA setup
      tags:
      - Authentication
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        description: Bearer token
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        description: Device identifier
        schema:
          type: string
      responses:
        '200':
          description: 2FA successfully enabled
        '401':
          description: Invalid OTP
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                otp:
                  type: string
                  example: '123456'
                  description: 6-digit OTP code from authenticator app
              required:
              - otp
  "/api/v1/auth/verify-2fa":
    post:
      summary: Verify 2FA and complete login
      tags:
      - Authentication
      parameters:
      - name: Device-ID
        in: header
        required: true
        description: Device identifier
        schema:
          type: string
      - name: App-Version
        in: header
        required: true
        description: App version
        schema:
          type: string
      - name: App-Build-Number
        in: header
        required: true
        description: App build number
        schema:
          type: string
      - name: Device-OS
        in: header
        required: true
        description: Device operating system
        schema:
          type: string
      responses:
        '200':
          description: Login successful
        '401':
          description: Missing device info
        '422':
          description: Missing required device parameters
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                temporary_token:
                  type: string
                  example: valid.temp.token
                  description: Temporary token from login response
                otp:
                  type: string
                  example: '123456'
                  description: 6-digit OTP code
                method:
                  type: string
                  example: email
                  description: 2FA method (email, sms, or totp)
              required:
              - temporary_token
              - otp
  "/api/v1/auth/request-2fa-method":
    post:
      summary: Request alternative 2FA method
      tags:
      - Authentication
      parameters: []
      responses:
        '200':
          description: 2FA challenge sent successfully
        '422':
          description: Invalid method
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                temporary_token:
                  type: string
                  example: valid.temp.token
                  description: Temporary token from login response
                method:
                  type: string
                  enum:
                  - sms
                  - email
                  example: email
                  description: Desired 2FA method
              required:
              - temporary_token
              - method
  "/api/v1/user_invitations":
    post:
      summary: Invite a user
      tags:
      - User Invitations
      security:
      - Bearer: []
      parameters:
      - name: Authorization
        in: header
        required: true
        example: Bearer <token>
        schema:
          type: string
      - name: Device-ID
        in: header
        required: true
        example: device-123
        schema:
          type: string
      responses:
        '200':
          description: Successfully invite with international phone number
        '422':
          description: Dealership UUID with a non dealership user
        '403':
          description: Dealership users can only invite other dealership users
        '401':
          description: Unauthorized when Device-ID is missing
        '404':
          description: Dealership not found
        '400':
          description: Missing required user parameter
        '500':
          description: Internal server error when service fails
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required:
              - user
              properties:
                user:
                  type: object
                  required:
                  - email
                  - first_name
                  - last_name
                  - phone
                  - user_type
                  - dealership_uuid
                  - role_type
                  properties:
                    email:
                      type: string
                      example: <EMAIL>
                    first_name:
                      type: string
                      example: Jane
                    last_name:
                      type: string
                      example: Smith
                    phone:
                      type: string
                      example: "+61412345678"
                    user_type:
                      type: string
                      example: dealership_user
                    dealership_uuid:
                      type: string
                      example: uuid-of-dealership
                    role_type:
                      type: string
                      example: dealership_admin
  "/api/v1/auth/verify-reset-code":
    post:
      summary: Verify Reset Code
      tags:
      - Authentication
      parameters: []
      responses:
        '200':
          description: code verified
        '422':
          description: missing inputs
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required:
              - temporary_token
              - reset_code
              properties:
                temporary_token:
                  type: string
                  example: temp.token
                reset_code:
                  type: string
                  example: '123456'
servers:
- url: http://localhost:{port}
  description: Development environment
  variables:
    port:
      default: '3000'
      enum:
      - '5000'
      - '4000'
      - '3000'
      - '2000'
      - '80'
- url: https://{defaultHost}
  description: Staging environment
  variables:
    defaultHost:
      default: morning-escarpment-47088-1580637df638.herokuapp.com
